using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using Newtonsoft.Json;
using RFQ.Dao;
using RFQ.Dao.Infrastructure;
using RFQ.Dao.Model;
using System.Web.SessionState;
using System.Dynamic;
using Newtonsoft.Json.Linq;
using WebApplication1.tool;
using OfficeOpenXml;
using System.IO;
using System.Data.Entity.Infrastructure;
using System.Data;
using System.Web.Script.Serialization;

namespace WebApplication1.ashx
{
    /// <summary>
    /// QuoteControl 的摘要说明
    /// </summary>
    public class QuoteControl : IHttpHandler, IRequiresSessionState
    {

        public void ProcessRequest(HttpContext context)
        {
            try
            {
                // 设置响应内容类型
                context.Response.ContentType = "application/json; charset=utf-8";

                // 验证action参数
                string action = context.Request.Params["action"];
                if (string.IsNullOrEmpty(action))
                {
                    WriteErrorResponse(context, "操作类型不能为空");
                    return;
                }

                switch (action)
                {
                    case "QueryBom":
                        QueryBomAndGroup(context);
                        break;
                    case "QueryAllQuote":
                        QueryAllQuote(context);
                        break;
                    case "QueryQuoteListById":
                        QueryQuoteListById(context);
                        break;
                    case "QuerySuppGroup":
                        QuerySuppGroup(context);
                        break;
                    case "QuerySelfQuote":
                        QuerySelfQuote(context);
                        break;
                    case "QuerySelfRFQ":
                        QuerySelfRFQ(context);
                        break;
                    case "QueryRecord":
                        QueryRecord(context);
                        break;
                    case "AcceptQuote":
                        UpdateQuoteStatus(context, 2);
                        break;
                    case "RejectQuote":
                        UpdateQuoteStatus(context, 1);
                        break;
                    case "DeleteQuote":
                        DeleteQuote(context);
                        break;
                    case "SendQuote":
                        SendQuote(context);
                        break;
                    case "UploadQuote":
                        UploadQuote(context);
                        break;
                    case "ParseQuoteFile":
                        ParseQuoteFile(context);
                        break;
                    case "UpdateQuoteStatus":
                        UpdateQuoteStatus(context);
                        break;
                    case "UploadFile":
                        UploadFile(context);
                        break;
                    case "DownLoadFile":
                        DownLoadFile(context);
                        break;
                    case "SubmitApproval":
                        SubmitApproval(context);
                        break;
                    case "DownloadTemplate":
                        DownloadTemplate(context);
                        break;
                    case "SaveQuote":
                        SaveQuote(context);
                        break;
                    default:
                        WriteErrorResponse(context, $"不支持的操作类型: {action}");
                        break;
                }
            }
            catch (Exception ex)
            {
                WriteErrorResponse(context, $"系统处理异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 统一的成功响应方法
        /// </summary>
        private void WriteSuccessResponse(HttpContext context, object data = null, string message = "操作成功")
        {
            var response = new { code = 0, msg = message, data = data };
            context.Response.Write(JsonConvert.SerializeObject(response));
        }

        /// <summary>
        /// 统一的错误响应方法
        /// </summary>
        private void WriteErrorResponse(HttpContext context, string message, int code = 1, object data = null)
        {
            var response = new { code = code, msg = message, data = data };
            context.Response.Write(JsonConvert.SerializeObject(response));
        }

        /// <summary>
        /// 验证必需参数
        /// </summary>
        private bool ValidateRequiredParam(HttpContext context, string paramName, out string paramValue)
        {
            paramValue = context.Request.Params[paramName];
            if (string.IsNullOrEmpty(paramValue))
            {
                WriteErrorResponse(context, $"参数 {paramName} 不能为空");
                return false;
            }
            return true;
        }

        //public void ProcessRequest(HttpContext context)
        //{
        //    context.Response.ContentType = "application/json";
        //    var response = new { code = 0, msg = "success", data = new List<object>() };

        //    try
        //    {
        //        switch (context.Request.HttpMethod)
        //        {
        //            case "GET":
        //                response = HandleQuery(context);
        //                break;
        //            case "POST":
        //                if (context.Request.Path.EndsWith("/delete"))
        //                {
        //                    response = HandleDelete(context);
        //                }
        //                break;
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        response = new { code = 500, msg = ex.Message, data = new List<object>() };
        //    }

        //    // 序列化响应
        //    context.Response.Write(JsonConvert.SerializeObject(response));
        //}

        public void QueryBomAndGroup(HttpContext context)
        {
            try
            {
                // 获取RFQ编号，优先从参数获取，其次从Session获取
                var no = context.Request.Params["RFQNo"] ?? (string)HttpContext.Current.Session["RFQNo"];

                if (string.IsNullOrEmpty(no))
                {
                    WriteErrorResponse(context, "RFQ编号不能为空，请检查参数或登录状态");
                    return;
                }

                // 查询RFQ模板信息
                var RfqTem = new BaseRepository<RFQTemplate>().FindSingle(u => u.RFQNO == no);
                if (RfqTem == null)
                {
                    WriteErrorResponse(context, $"未找到RFQ编号为 {no} 的模板信息");
                    return;
                }

                if (string.IsNullOrEmpty(RfqTem.CUSTOMERCODE) || string.IsNullOrEmpty(RfqTem.TEMPLATECODE))
                {
                    WriteErrorResponse(context, "RFQ模板信息不完整，缺少客户代码或模板代码");
                    return;
                }

                string customercode = RfqTem.CUSTOMERCODE;
                string templatecode = RfqTem.TEMPLATECODE;
                // 获取BOM数据
                var boms = new DbHelper().GetRFQData(no, customercode, templatecode);
                if (boms == null || boms.Rows.Count == 0)
                {
                    WriteErrorResponse(context, "未找到相关的BOM数据");
                    return;
                }

                // 获取所有的 BOM_ID
                var bomIds = boms.AsEnumerable()
                    .Where(r => r["ID"] != null && !string.IsNullOrEmpty(r["ID"].ToString()))
                    .Select(r => r["ID"].ToString())
                    .ToList();

                if (!bomIds.Any())
                {
                    WriteErrorResponse(context, "BOM数据中未找到有效的ID");
                    return;
                }

                // 先根据 BOM_ID 筛选，再筛选 REQUIREQUOTE = 1 的记录
                var matchTipRepository = new BaseRepository<MatchTipData>();
                var matchTipData = matchTipRepository.Find(m =>
                    bomIds.Contains(m.BOM_ID) &&
                    m.REQUIRE_QUOTE == 1
                ).ToList();

                // 为了提高查找效率，将 matchTipData 转换为字典
                var matchTipDict = matchTipData.ToDictionary(m => m.BOM_ID);

                // 为 DataTable 添加新列
                if (!boms.Columns.Contains("MATCH_PARTNUMBER"))
                    boms.Columns.Add("MATCH_PARTNUMBER", typeof(string));
                if (!boms.Columns.Contains("MATCH_CUST"))
                    boms.Columns.Add("MATCH_CUST", typeof(string));
                if (!boms.Columns.Contains("MATCH_MANUFACTURER"))
                    boms.Columns.Add("MATCH_MANUFACTURER", typeof(string));
                if (!boms.Columns.Contains("MATCH_MPN"))
                    boms.Columns.Add("MATCH_MPN", typeof(string));

                // 创建一个新的 DataTable 来存储过滤后的数据
                DataTable filteredBoms = boms.Clone();

                // 只处理匹配的数据
                foreach (var matchItem in matchTipDict)
                {
                    // 找到对应的原始行
                    var matchingRows = boms.AsEnumerable()
                        .Where(row => row["ID"].ToString() == matchItem.Key);

                    foreach (var originalRow in matchingRows)
                    {
                        DataRow newRow = filteredBoms.NewRow();
                        // 复制原始行的所有数据
                        foreach (DataColumn col in boms.Columns)
                        {
                            newRow[col.ColumnName] = originalRow[col.ColumnName];
                        }

                        // 添加匹配的数据
                        newRow["MATCH_PARTNUMBER"] = matchItem.Value.MATCH_PARTNUMBER == null ? DBNull.Value : (object)matchItem.Value.MATCH_PARTNUMBER;
                        newRow["MATCH_CUST"] = matchItem.Value.MATCH_CUST == null ? DBNull.Value : (object)matchItem.Value.MATCH_CUST;
                        newRow["MATCH_MANUFACTURER"] = matchItem.Value.MATCH_MANUFACTURER == null ? DBNull.Value : (object)matchItem.Value.MATCH_MANUFACTURER;
                        newRow["MATCH_MPN"] = matchItem.Value.MATCH_MPN == null ? DBNull.Value : (object)matchItem.Value.MATCH_MPN;

                        filteredBoms.Rows.Add(newRow);
                    }
                }
                // 获取物料组数据
                var materialgroups = new BaseRepository<MaterialGroup>().Find(null).ToList();
                if (materialgroups == null)
                {
                    materialgroups = new List<MaterialGroup>(); // 确保不返回null
                }

                // 构建响应数据
                var responseData = new
                {
                    bomdata = filteredBoms,
                    groupdata = materialgroups
                };

                WriteSuccessResponse(context, responseData, "查询BOM和物料组数据成功");
            }
            catch (Exception ex)
            {
                WriteErrorResponse(context, $"查询BOM和物料组数据失败: {ex.Message}");
            }
        }

        public void QueryQuoteListById(HttpContext context)
        {
            try
            {
                // 验证bomId参数
                string bomId;
                if (!ValidateRequiredParam(context, "bomId", out bomId))
                {
                    return;
                }

                // 查询报价记录
                var result = new BaseRepository<QuoRecord>().Find(u => u.BOM_ID == bomId);
                if (result == null)
                {
                    WriteErrorResponse(context, $"未找到BOM ID为 {bomId} 的报价记录");
                    return;
                }
                var resultList = result.ToList();
                if (!resultList.Any())
                {
                    WriteSuccessResponse(context, new List<object>(), "未找到相关报价记录");
                    return;
                }

                var formattedResult = resultList.Select(item => new {
                    // 复制原有属性
                    QUO_ID = item.Id,
                    QUO_PRICE = item.QUO_PRICE,
                    QUO_MFR = item.QUO_MFR,
                    QUO_MPN = item.QUO_MPN,
                    ALTER_MPN = item.ALTER_MPN,
                    QUO_SUPP = item.QUO_SUPP,
                    QUO_CUR = item.QUO_CUR,
                    QUO_USPRICE = item.QUO_USPRICE,
                    QUO_RMBPRICE = item.QUO_RMBPRICE,
                    SPQ = item.SPQ,
                    MOQ = item.MOQ,
                    LT = item.LT,
                    TOOL_COST = item.TOOL_COST,
                    TOOL_CURR = item.TOOL_CURR,
                    // 格式化CreateTime，添加空值检查
                    CREATE_DATE = item.CREATE_DATE.ToString("yyyy-MM-dd")
                }).ToList();

                WriteSuccessResponse(context, formattedResult, "查询报价列表成功");
            }
            catch (Exception ex)
            {
                WriteErrorResponse(context, $"查询报价列表失败: {ex.Message}");
            }
        }

        public void QuerySuppGroup(HttpContext context)
        {
            try
            {
                // 查询供应商组数据
                var result = new BaseRepository<SupplierGroup>().Find(null).ToList();
                if (result == null)
                {
                    result = new List<SupplierGroup>(); // 确保不返回null
                }

                WriteSuccessResponse(context, result, "查询供应商组数据成功");
            }
            catch (Exception ex)
            {
                WriteErrorResponse(context, $"查询供应商组数据失败: {ex.Message}");
            }
        }

        public void QueryAllQuote(HttpContext context)
        {
            try
            {
                // 获取查询参数
                string No = context.Request.Params["RFQNo"] ?? "";

                using (var db = new RFQDBContext())
                {
                    var query = db.QuoStatuses
                        .AsNoTracking()  // 提高查询性能
                        .Where(q => 1 == 1); // 初始条件

                    // 添加查询条件
                    if (!string.IsNullOrEmpty(No))
                    {
                        query = query.Where(q => q.RFQ_NO == No);
                    }

                    // 执行查询并转换结果
                    var queryResult = query
                        .OrderByDescending(q => q.CREATE_DATE)
                        .Select(q => new
                        {
                            q.QUO_NO,
                            q.MATERIAL_GROUP,
                            q.CREATE_DATE,
                            q.QUO_DATE,
                            q.QUO_STATUS,
                            q.FILE_UPLOAD1,
                            q.FILE_UPLOAD2,
                            q.FILE_UPLOAD3
                        })
                        .ToList(); // 执行查询，获取数据到内存

                    // 在内存中进行日期格式化
                    var result = queryResult.Select(q => new
                    {
                        quotationNo = q.QUO_NO ?? "",
                        materialGroup = q.MATERIAL_GROUP ?? "",
                        createDate = q.CREATE_DATE.ToString("yyyy-MM-dd"),
                        quotationDate = q.QUO_DATE.HasValue ?
                            q.QUO_DATE.Value.ToString("yyyy-MM-dd") : "",
                        status = q.QUO_STATUS,
                        file1 = q.FILE_UPLOAD1 ?? "",
                        file2 = q.FILE_UPLOAD2 ?? "",
                        file3 = q.FILE_UPLOAD3 ?? ""
                    }).ToList();

                    WriteSuccessResponse(context, result, "查询所有报价数据成功");
                }
            }
            catch (Exception ex)
            {
                WriteErrorResponse(context, $"查询所有报价数据失败: {ex.Message}");
            }
        }

        public void QuerySelfRFQ(HttpContext context)
        {
            try
            {
                // 获取查询参数
                string paramNo = context.Request.QueryString["RFQNo"] ?? "";

                // 获取当前用户信息
                User user = (User)HttpContext.Current.Session["UserInfo"];
                if (user == null)
                {
                    WriteErrorResponse(context, "用户未登录，请重新登录");
                    return;
                }

                string userName = user.UserName;
                if (string.IsNullOrEmpty(userName))
                {
                    WriteErrorResponse(context, "用户信息不完整，请重新登录");
                    return;
                }

                // 查询项目信息
                var result = new BaseRepository<ProjectInfo>().Find(u => u.CreateUser == userName);

                // 添加查询条件
                if (!string.IsNullOrEmpty(paramNo))
                {
                    result = result.Where(q => q.RFQNo.Contains(paramNo));
                }

                var resultList = result.ToList();
                if (resultList == null)
                {
                    resultList = new List<ProjectInfo>(); // 确保不返回null
                }

                var finresult = resultList.Select(item => new
                {
                    item.Id,
                    RFQNo = item.RFQNo ?? "",
                    Status = item.Status ?? "",
                    CreateUser = item.CreateUser ?? "",
                    CreateTime = item.CreateTime.ToString("yyyy-MM-dd")
                }).ToList();

                WriteSuccessResponse(context, finresult, "查询自己的RFQ数据成功");
            }
            catch (Exception ex)
            {
                WriteErrorResponse(context, $"查询自己的RFQ数据失败: {ex.Message}");
            }
        }

        public void QuerySelfQuote(HttpContext context)
        {
            try
            {
                // 获取当前用户信息
                User user = (User)HttpContext.Current.Session["UserInfo"];
                if (user == null)
                {
                    WriteErrorResponse(context, "用户未登录，请重新登录");
                    return;
                }

                string suppno = user.UserName;
                if (string.IsNullOrEmpty(suppno))
                {
                    WriteErrorResponse(context, "用户信息不完整，请重新登录");
                    return;
                }

                // 获取总数
                var count = new BaseRepository<QuoStatus>().GetCount(null);

                // 获取数据并转换日期格式
                var allData = new BaseRepository<QuoStatus>().Find(null).ToList();
                if (allData == null)
                {
                    allData = new List<QuoStatus>(); // 确保不返回null
                }

                var result = allData
                    .Select(item => new
                    {
                        // 保留其他原有属性
                        item.Id,
                        RFQ_NO = item.RFQ_NO ?? "",
                        QUO_NO = item.QUO_NO ?? "",
                        MATERIAL_GROUP = item.MATERIAL_GROUP ?? "",
                        item.QUO_STATUS,
                        FILE_UPLOAD1 = ExtractFileName(item.FILE_UPLOAD1),
                        FILE_UPLOAD2 = ExtractFileName(item.FILE_UPLOAD2),
                        FILE_UPLOAD3 = ExtractFileName(item.FILE_UPLOAD3),

                        // 格式化日期
                        CREATE_DATE = item.CREATE_DATE.ToString("yyyy-MM-dd"),
                        QUO_DATE = item.QUO_DATE.HasValue ?
                            item.QUO_DATE.Value.ToString("yyyy-MM-dd") : ""
                    })
                    .Where(u => !string.IsNullOrEmpty(u.QUO_NO) &&
                               u.QUO_NO.Split('-').Length > 1 &&
                               u.QUO_NO.Split('-')[1] == suppno &&
                               (u.QUO_STATUS == 0 || u.QUO_STATUS == 2))
                    .ToList();

                var responseData = new
                {
                    data = result,
                    count = count
                };

                WriteSuccessResponse(context, responseData, "查询自己的报价数据成功");
            }
            catch (Exception ex)
            {
                WriteErrorResponse(context, $"查询自己的报价数据失败: {ex.Message}");
            }
        }

        public void QueryRecord(HttpContext context)
        {
            try
            {
                // 验证报价单号参数
                string quono;
                if (!ValidateRequiredParam(context, "quoteNo", out quono))
                {
                    return;
                }

                // 查询报价记录
                var result = new BaseRepository<QuoRecord>().Find(u => u.QUO_NO == quono);
                var count = new BaseRepository<QuoRecord>().GetCount(u => u.QUO_NO == quono);

                var resultList = result?.ToList();
                if (resultList == null)
                {
                    resultList = new List<QuoRecord>(); // 确保不返回null
                }

                var responseData = new
                {
                    data = resultList,
                    count = count
                };

                WriteSuccessResponse(context, responseData, "查询报价记录成功");
            }
            catch (Exception ex)
            {
                WriteErrorResponse(context, $"查询报价记录失败: {ex.Message}");
            }
        }

        public void UpdateQuoteStatus(HttpContext context, int status)
        {
            try
            {
                // 验证报价单号参数
                string quono;
                if (!ValidateRequiredParam(context, "quoteNo", out quono))
                {
                    return;
                }

                // 验证状态值
                if (status < 0 || status > 3)
                {
                    WriteErrorResponse(context, "状态值无效，必须在0-3之间");
                    return;
                }

                // 查找报价状态记录
                var st = new BaseRepository<QuoStatus>().FindSingle(u => u.QUO_NO == quono);
                if (st == null)
                {
                    WriteErrorResponse(context, $"未找到报价单号为 {quono} 的记录");
                    return;
                }

                // 更新状态
                new BaseRepository<QuoStatus>().Update(u => u.QUO_NO == quono, u => new QuoStatus
                {
                    QUO_STATUS = status, //更新状态标识 0 未报价 1 拒绝报价 2 报价中 3 已报价
                });

                // 检查报价状态
                if (!string.IsNullOrEmpty(st.RFQ_NO))
                {
                    CheckQuoStatus(st.RFQ_NO);
                }

                string statusText = GetStatusText(status);
                WriteSuccessResponse(context, null, $"报价状态更新为 {statusText} 成功");
            }
            catch (Exception ex)
            {
                WriteErrorResponse(context, $"更新报价状态失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取状态文本描述
        /// </summary>
        private string GetStatusText(int status)
        {
            switch (status)
            {
                case 0: return "未报价";
                case 1: return "拒绝报价";
                case 2: return "报价中";
                case 3: return "已报价";
                default: return "未知状态";
            }
        }

        public void SendQuote(HttpContext context)
        {
            try
            {
                // 验证供应商参数
                string selectsupp;
                if (!ValidateRequiredParam(context, "suppliers", out selectsupp))
                {
                    return;
                }

                // 获取RFQ编号
                var no = (string)HttpContext.Current.Session["RFQNo"];
                if (string.IsNullOrEmpty(no))
                {
                    WriteErrorResponse(context, "RFQ编号不能为空，请检查Session状态");
                    return;
                }

                string date = DateTime.Now.ToString("yyyyMMdd");
                string quoNo = "";
                List<QuoRecord> qus = new List<QuoRecord>();
                Dictionary<string, string> MailList = new Dictionary<string, string>();

                // 解析供应商JSON数据
                object suppobj;
                try
                {
                    suppobj = JsonConvert.DeserializeObject(selectsupp);
                }
                catch (JsonException ex)
                {
                    WriteErrorResponse(context, $"供应商数据格式错误: {ex.Message}");
                    return;
                }

                RFQDBContext dbcontext = new RFQDBContext();
                foreach (var x in suppobj as JObject) // key为物料组名 value为供应商id
                {
                    var idarr = x.Value.ToArray();
                    foreach (string id in idarr)
                    {
                        if (string.IsNullOrEmpty(id))
                        {
                            continue; // 跳过空的ID
                        }

                        var supp = new BaseRepository<SupplierGroup>().FindSingle(u => u.Id == id);
                        if (supp == null)
                        {
                            WriteErrorResponse(context, $"未找到ID为 {id} 的供应商组信息");
                            return;
                        }

                        if (string.IsNullOrEmpty(supp.SUPP_NO))
                        {
                            WriteErrorResponse(context, $"供应商组 {id} 的供应商编号为空");
                            return;
                        }

                        var suppno = supp.SUPP_NO;
                        var info = new BaseRepository<SupplierInfo>().FindSingle(u => u.SUPP_NO == suppno);
                        if (info == null)
                        {
                            WriteErrorResponse(context, $"未找到供应商编号为 {suppno} 的供应商信息");
                            return;
                        }

                        if (string.IsNullOrEmpty(info.SUPP_MAIL))
                        {
                            WriteErrorResponse(context, $"供应商 {suppno} 的邮箱地址为空");
                            return;
                        }
                        var boms = from bom in dbcontext.Set<BomInfo>()
                                   join match in dbcontext.Set<MatchTipData>()
                                       on bom.Id equals match.BOM_ID
                                   where bom.MATERIAL_GROUP == supp.MATERIAL_GROUP
                                       && bom.RFQNO == no
                                       && match.REQUIRE_QUOTE == 1
                                   select bom;
                        var serialnum = new BaseRepository<QuoRecord>().Find(u => u.QUO_NO.Contains(date)).OrderByDescending(u => u.QUO_NO.Substring(u.QUO_NO.Length-1,1)).FirstOrDefault();
                        quoNo = GetSerialNumber(serialnum, supp.SUPP_NO);
                        QuoStatus st = new QuoStatus();
                        st.RFQ_NO = no;
                        st.QUO_NO = quoNo;
                        st.MATERIAL_GROUP= supp.MATERIAL_GROUP;
                        st.CREATE_DATE = DateTime.Now;
                        st.QUO_STATUS = 0;
                        foreach (var bom in boms)
                        {
                            QuoRecord qu = new QuoRecord();
                            qu.BOM_ID = bom.Id;
                            qu.RFQ_NO = no;
                            qu.QUO_NO = quoNo;
                            qu.BOM_DESC = bom.BOM_DESC;
                            qu.MATERIAL_GROUP = supp.MATERIAL_GROUP;
                            qu.QUO_SUPP = supp.SUPP_NAME;
                            qu.CUST_PARTNUMBER = bom.CUST_PARTNUMBER;
                            qu.CREATE_DATE = DateTime.Now;
                            qus.Add(qu);
                        }
                        if (qus.Count() > 0)
                        {
                            new BaseRepository<QuoStatus>().Add(st);
                            new BaseRepository<QuoRecord>().BatchAdd(qus.ToArray());
                        }
                        qus.Clear();
                        MailList.Add(quoNo, info.SUPP_MAIL);
                    }
                }

                // 发送邮件
                int successMailCount = 0;
                int failMailCount = 0;
                foreach (var i in MailList)
                {
                    try
                    {
                        SendSuppMail(i.Value, i.Key);
                        successMailCount++;
                    }
                    catch (Exception mailEx)
                    {
                        failMailCount++;
                        // 记录邮件发送失败，但不中断整个流程
                        // 可以考虑添加日志记录
                    }
                }

                // 更新项目状态
                new BaseRepository<ProjectInfo>().Update(u => u.RFQNo == no, u => new ProjectInfo
                {
                    Status = "4", //更新状态标识 发送报价结束 等待供应商回传报价
                });

                string message = $"报价发送完成，成功发送 {successMailCount} 封邮件";
                if (failMailCount > 0)
                {
                    message += $"，失败 {failMailCount} 封邮件";
                }

                WriteSuccessResponse(context, null, message);
            }
            catch (Exception ex)
            {
                WriteErrorResponse(context, $"发送报价失败: {ex.Message}");
            }
        }

        public void SendSuppMail(string recv,string quono)
        {
            try
            {
                var records = new BaseRepository<QuoRecord>().Find(u => u.QUO_NO == quono);
                string send = "<EMAIL>";
                string title = "RFQ报价通知,单号:"+quono;
                string body = "请前往RFQ系统提供报价！https://172.20.0.14/RFQSystem/";
                SendMail sm = new SendMail();
                string path=CreateExcel(records, quono);
                sm.AttachFiles.Add(path);
                bool sendResultFlag = sm.SendMails(send, recv, title, body, "mail.hanajx.com", "ftpnotice", "a.2017");
                  

            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public string CreateExcel(IQueryable<QuoRecord> records,string quono)
        {
            string filePath = @"\\172.20.0.14\System Static\RFQSystem";
            string fileName = $"{quono}_{DateTime.Now:yyyyMMddHHmmss}.xlsx";
            string fullPath = Path.Combine(filePath, fileName);

            FileInfo file = new FileInfo(fullPath);

            using (ExcelPackage package = new ExcelPackage(file))
            {
                ExcelWorksheet worksheet = package.Workbook.Worksheets.Add("QuoRecord");

                // 添加表头
                worksheet.Cells[1, 1].Value = "系统Id(不要改动)";
                worksheet.Cells[1, 2].Value = "报价单号"; // 根据实际列名添加
                worksheet.Cells[1, 3].Value = "客户料号";
                worksheet.Cells[1, 4].Value = "物料描述";
                worksheet.Cells[1, 5].Value = "物料组别";
                worksheet.Cells[1, 6].Value = "供应商名称";
                worksheet.Cells[1, 7].Value = "MFR";
                worksheet.Cells[1, 8].Value = "MPN";
                worksheet.Cells[1, 9].Value = "ALTER_MPN(YES/NO)";
                worksheet.Cells[1, 10].Value = "报价币别";
                worksheet.Cells[1, 11].Value = "单价";
                worksheet.Cells[1, 12].Value = "单价(US)";
                worksheet.Cells[1, 13].Value = "SPQ";
                worksheet.Cells[1, 14].Value = "MOQ";
                worksheet.Cells[1, 15].Value = "LT";
                worksheet.Cells[1, 16].Value = "TOOLING COST";
                worksheet.Cells[1, 17].Value = "TOOLING CURR";

                // 填充数据
                int row = 2;
                foreach (var record in records)
                {
                    worksheet.Cells[row, 1].Value = record.Id;
                    worksheet.Cells[row, 2].Value = record.QUO_NO; // 根据实际属性添加
                    worksheet.Cells[row, 3].Value = record.CUST_PARTNUMBER;
                    worksheet.Cells[row, 4].Value = record.BOM_DESC;
                    worksheet.Cells[row, 5].Value = record.MATERIAL_GROUP;
                    worksheet.Cells[row, 6].Value = record.QUO_SUPP;
                    row++;
                }

                // 自动调整列宽
                worksheet.Cells.AutoFitColumns();

                // 保存 Excel 文件
                package.Save();
            }
            return fullPath;
        }

        public void UploadQuote(HttpContext context)
        {
            try
            {
                // 验证文件上传
                if (context.Request.Files.Count == 0)
                {
                    WriteErrorResponse(context, "未接收到上传文件");
                    return;
                }

                var file = context.Request.Files[0];
                if (file == null || file.ContentLength <= 0)
                {
                    WriteErrorResponse(context, "上传的文件无效或为空");
                    return;
                }

                // 验证报价单号参数
                string quono;
                if (!ValidateRequiredParam(context, "quoteNo", out quono))
                {
                    return;
                }

                // 验证文件格式
                string fileExtension = Path.GetExtension(file.FileName).ToLower();
                if (fileExtension != ".xlsx" && fileExtension != ".xls")
                {
                    WriteErrorResponse(context, "只支持Excel文件格式(.xlsx或.xls)");
                    return;
                }

                // 读取 Excel 文件内容
                using (var excelPack = new ExcelPackage(file.InputStream))
                {
                    if (excelPack.Workbook.Worksheets.Count == 0)
                    {
                        WriteErrorResponse(context, "Excel文件中没有工作表");
                        return;
                    }

                    var ws = excelPack.Workbook.Worksheets[0];
                    if (ws.Dimension == null || ws.Dimension.Rows <= 1)
                    {
                        WriteErrorResponse(context, "Excel文件中没有数据行");
                        return;
                    }

                    int rowCount = ws.Dimension.Rows;

                        // 记录处理结果
                        int successCount = 0;
                        int failCount = 0;
                        List<string> failedIds = new List<string>();
                        // 从第二行开始读取数据（跳过表头）
                        using (var db = new RFQDBContext())
                        {
                            var result = db.QuoRecords.Where(u => u.QUO_NO == quono).ToList();
                            var rfqno = result[0].RFQ_NO;
                            var projectcur = db.ProjectInfos.Where(u => u.RFQNo == rfqno).FirstOrDefault().SuppCur;
                            decimal? projectrate=0;
                            if (projectcur != null)
                                projectrate = db.ProjectInfos.Where(u => u.RFQNo == rfqno).FirstOrDefault().Rate;
                            for (int row = 2; row <= rowCount; row++)
                            {
                                // 读取ID
                                string id = ws.Cells[row, 1].Value?.ToString();

                                // 检查ID是否为空
                                if (string.IsNullOrWhiteSpace(id))
                                {
                                    failCount++;
                                    continue;
                                }

                                try
                                {
                                    // 查找对应的数据库记录
                                    var quote = result.Where(q => q.Id == id).FirstOrDefault();
                                    if (quote == null)
                                    {
                                        failCount++;
                                        failedIds.Add(id);
                                        continue;
                                    }
                                    

                                    // 更新数据
                                    quote.QUO_MFR = ws.Cells[row, 7].Value?.ToString();
                                    quote.QUO_MPN = ws.Cells[row, 8].Value?.ToString();
                                    quote.ALTER_MPN = ws.Cells[row, 9].Value?.ToString();
                                    quote.QUO_CUR = ws.Cells[row, 10].Value?.ToString().Trim();

                                    if(String.IsNullOrEmpty(quote.QUO_CUR))
                                    {
                                        failCount++;
                                        failedIds.Add(id);
                                        continue;
                                    }

                                    var currency = new BaseRepository<Rate>().FindSingle(u => u.CURRENCY.Contains(quote.QUO_CUR));
                                    

                                    // 处理价格字段，确保是数值
                                    decimal unitPrice;
                                    if (decimal.TryParse(ws.Cells[row, 11].Value?.ToString(), out unitPrice))
                                        quote.QUO_PRICE = unitPrice;

                                    decimal unitPriceUS;
                                    if (decimal.TryParse(ws.Cells[row, 12].Value?.ToString(), out unitPriceUS))
                                        quote.QUO_USPRICE = unitPriceUS;
                                    

                                    if (currency != null && unitPrice != 0&& projectrate!=0)
                                        quote.QUO_RMBPRICE = (unitPrice * currency.RATE)/projectrate;

                                    // 处理数量字段
                                    //int minOrder;
                                    //if (int.TryParse(ws.Cells[row, 13].Value?.ToString(), out minOrder))
                                    //    quote.ORDER_MIN = minOrder;
                                    quote.SPQ = int.Parse(ws.Cells[row, 13].Value?.ToString().Trim());
                                    quote.MOQ = int.Parse(ws.Cells[row, 14].Value?.ToString().Trim());
                                    quote.LT = int.Parse(ws.Cells[row, 15].Value?.ToString().Trim());
                                    quote.TOOL_COST = decimal.Parse(ws.Cells[row, 16].Value?.ToString().Trim());
                                    quote.TOOL_CURR = ws.Cells[row, 17].Value?.ToString().Trim();

                                    successCount++;
                                }
                                catch (Exception ex)
                                {
                                    // 记录具体行的处理失败
                                    failCount++;
                                    failedIds.Add(id);
                                }
                            }

                            // 保存所有更改
                            db.SaveChanges();
                        }
                        var responseData = new
                        {
                            successCount = successCount,
                            failCount = failCount,
                            failedIds = failedIds,
                            message = $"处理完成：成功{successCount}条，失败{failCount}条"
                        };

                        WriteSuccessResponse(context, responseData, "报价文件上传处理完成");
                    }
                }
            
            catch (Exception ex)
            {
                WriteErrorResponse(context, $"读取报价文件失败，请检查文件格式是否正确: {ex.Message}");
            }
        }

        public void DeleteQuote(HttpContext context)
        {
            try
            {
                // 验证删除ID参数
                string idsJson = context.Request.Params["ids[]"];
                if (string.IsNullOrEmpty(idsJson))
                {
                    WriteErrorResponse(context, "未提供要删除的ID", 400);
                    return;
                }

                string[] ids = idsJson.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
                if (ids.Length == 0)
                {
                    WriteErrorResponse(context, "删除ID列表为空", 400);
                    return;
                }

                using (var db = new RFQDBContext())
                {
                    var items = db.QuoStatuses
                        .Where(q => ids.Contains(q.QUO_NO))
                        .ToList();

                    if (!items.Any())
                    {
                        WriteErrorResponse(context, "未找到要删除的记录", 404);
                        return;
                    }

                    // 记录删除的数量
                    int deleteCount = items.Count;

                    db.QuoStatuses.RemoveRange(items);
                    db.SaveChanges();

                    WriteSuccessResponse(context, new { deletedCount = deleteCount }, $"成功删除 {deleteCount} 条记录");
                }
            }
            catch (Exception ex)
            {
                WriteErrorResponse(context, $"删除失败: {ex.Message}", 500);
            }
        }

        public string GetSerialNumber(QuoRecord serialNumber, string suppno)
        {
            if (serialNumber != null)
            {
                string headDate = serialNumber.QUO_NO.Substring(serialNumber.QUO_NO.Length - 12, 8);
                int lastNumber = int.Parse(serialNumber.QUO_NO.Substring(serialNumber.QUO_NO.Length - 3));
                //如果数据库最大值流水号中日期和生成日期在同一天，则顺序号加1
                if (headDate == DateTime.Now.ToString("yyyyMMdd"))
                {
                    lastNumber++;
                    return "QUO-" + suppno + "-" + headDate + "-" + lastNumber.ToString("000");
                }
            }
            return "QUO-" + suppno + "-" + DateTime.Now.ToString("yyyyMMdd") + "-" + "001";
        }

        public void UpdateQuoteStatus(HttpContext context)
        {

            dynamic d = new ExpandoObject();
            try
            {
                var quono = context.Request.Params["quoteNo"];
                var st = new BaseRepository<QuoStatus>().FindSingle(u => u.QUO_NO == quono);
                new BaseRepository<QuoStatus>().Update(u => u.QUO_NO == quono, u => new QuoStatus
                {

                    QUO_STATUS = 3, //更新状态标识
                    QUO_DATE = DateTime.Now,

                });
                CheckQuoStatus(st.RFQ_NO);
                d.code = 0;
                d.msg = "更新成功";
                context.Response.Write(JsonConvert.SerializeObject(d));
            }
            catch (Exception ex)
            {
                d.code = -1;
                d.msg = ex.Message;
                context.Response.Write(JsonConvert.SerializeObject(d));
            }
        }

        public void UploadFile(HttpContext context)
        {
            try
            {
                // 确保请求包含文件
                if (context.Request.Files.Count == 0)
                {
                    context.Response.Write(JsonConvert.SerializeObject(new { code = 1, msg = "未接收到文件" }));
                    return;
                }

                HttpPostedFile file = context.Request.Files[0];
                
                // 从Form中获取参数
                string quoteNo = context.Request.Form["quoteNo"] ?? context.Request.QueryString["quoteNo"];
                string fileIndex = context.Request.Form["fileIndex"] ?? context.Request.QueryString["fileIndex"];
                
                // 验证必要参数
                if (string.IsNullOrEmpty(quoteNo))
                {
                    context.Response.Write(JsonConvert.SerializeObject(new { code = 1, msg = "报价单号不能为空" }));
                    return;
                }
                
                if (string.IsNullOrEmpty(fileIndex))
                {
                    context.Response.Write(JsonConvert.SerializeObject(new { code = 1, msg = "文件索引不能为空" }));
                    return;
                }

                if (file == null || file.ContentLength <= 0)
                {
                    context.Response.Write(JsonConvert.SerializeObject(new { code = 1, msg = "上传的文件无效" }));
                    return;
                }

                var dbcontext = new RFQDBContext();

                // 创建上传文件夹（如果不存在）
                string uploadPath = @"\\172.20.0.14\System Static\RFQSystem";
                if (!Directory.Exists(uploadPath))
                {
                    Directory.CreateDirectory(uploadPath);
                }

                // 生成文件名（使用时间戳避免重复）
                string fileName = Path.GetFileNameWithoutExtension(file.FileName);
                string fileExt = Path.GetExtension(file.FileName);
                string newFileName = $"{fileName}_{DateTime.Now:yyyyMMddHHmmss}{fileExt}";
                string filePath = Path.Combine(uploadPath, newFileName);

                // 保存文件
                file.SaveAs(filePath);

                // 更新数据库
                var quodata = dbcontext.QuoStatuses.FirstOrDefault(u => u.QUO_NO == quoteNo);
                if (quodata == null)
                {
                    context.Response.Write(JsonConvert.SerializeObject(new { code = 1, msg = "找不到该报价单" }));
                    return;
                }
                
                // 使用int.Parse尝试解析文件索引，确保它是有效的数字
                int fileIdx;
                if (!int.TryParse(fileIndex, out fileIdx) || fileIdx < 1 || fileIdx > 3)
                {
                    context.Response.Write(JsonConvert.SerializeObject(new { code = 1, msg = "文件索引无效，必须是1-3之间的数字" }));
                    return;
                }
                
                switch(fileIdx)
                {
                    case 1:
                        quodata.FILE_UPLOAD1 = filePath;
                        break;
                    case 2:
                        quodata.FILE_UPLOAD2 = filePath;
                        break;
                    case 3:
                        quodata.FILE_UPLOAD3 = filePath;
                        break;
                    default:
                        throw new ArgumentException("无效的文件索引", nameof(fileIndex));
                }
                
                dbcontext.SaveChanges();
                
                // 返回成功响应
                context.Response.Write(JsonConvert.SerializeObject(new { 
                    code = 0, 
                    msg = "上传成功", 
                    data = new { 
                        filePath = filePath,
                        fileName = Path.GetFileName(filePath)
                    } 
                }));
            }
            catch (Exception ex)
            {
                context.Response.Write(JsonConvert.SerializeObject(new { code = 1, msg = "上传失败：" + ex.Message }));
            }
        }

        public void DownLoadFile(HttpContext context)
        {
            try
            {
                string filePath = context.Request.QueryString["filePath"];
                if (string.IsNullOrEmpty(filePath))
                {
                    ResponseError(context, "文件路径不能为空");
                    return;
                }

                // 解码文件路径
                filePath = HttpUtility.UrlDecode(filePath);

                // 获取完整的物理路径
                string fullPath = filePath.TrimStart('/');

                

                if (!File.Exists(fullPath))
                {
                    ResponseError(context, "文件不存在");
                    return;
                }

                // 获取文件信息
                FileInfo fileInfo = new FileInfo(fullPath);

                // 设置响应头
                context.Response.Clear();
                context.Response.ClearHeaders();
                context.Response.ClearContent();
                context.Response.AddHeader("Content-Type", GetContentType(fileInfo.Extension));
                context.Response.AddHeader("Content-Disposition", $"attachment; filename*=UTF-8''{HttpUtility.UrlEncode(fileInfo.Name)}");
                context.Response.AddHeader("Content-Length", fileInfo.Length.ToString());

                // 输出文件内容
                using (FileStream fs = new FileStream(fullPath, FileMode.Open, FileAccess.Read))
                {
                    byte[] buffer = new byte[64 * 1024]; // 64KB buffer
                    int read;
                    while ((read = fs.Read(buffer, 0, buffer.Length)) > 0)
                    {
                        context.Response.OutputStream.Write(buffer, 0, read);
                    }
                    context.Response.Flush();
                }
            }
            catch (Exception ex)
            {
                ResponseError(context, "下载失败: " + ex.Message);
            }
        }

        public void SubmitApproval(HttpContext context)
        {
            try
            {
                var rfqno = context.Request.Params["RFQNo"].ToString();
                JArray approvalarr = (JArray)JsonConvert.DeserializeObject(context.Request.Params["approvalData"]);
                User user = (User)HttpContext.Current.Session["UserInfo"];
                var name = user.UserName;
                var exist_form = new BaseRepository<FlowForm>().FindSingle(u => u.RFQNO == rfqno&&u.FLOWSTATUS!=0); //检查有没有已经开始的审批
                if(exist_form!=null)
                {
                    context.Response.Write(JsonConvert.SerializeObject(new { code = 1, msg = "已经存在该RFQ编号的审批单!" }));
                    return;
                }
                foreach (JObject item in approvalarr)
                {
                    string bomId = item["bomId"].ToString();
                    string quotationId = item["quotationId"].ToString();
                    string remark = item["remark"]?.ToString();

                    var quodata = new BaseRepository<QuoRecord>().FindSingle(u => u.Id == quotationId);

                    if (quodata != null)
                    {
                        new BaseRepository<BomInfo>().Update(u => u.Id == bomId, u => new BomInfo
                        {
                            QUO_MFR = quodata.QUO_MFR,
                            QUO_MPN = quodata.QUO_MPN,
                            ALTER_MPN = quodata.ALTER_MPN,
                            SUP_SELECTED = quodata.QUO_SUPP,
                            PUR_CUR = quodata.QUO_CUR,
                            UNIT_PRICE = quodata.QUO_RMBPRICE,
                            US_PRICE = quodata.QUO_USPRICE,
                            SPQ = quodata.SPQ,
                            MOQ = quodata.MOQ,
                            LEAD_TIME = quodata.LT,
                            TOOL_COST = quodata.TOOL_COST,
                            TOOL_CURR = quodata.TOOL_CURR,
                            REMARK_SOURC = remark,

                        });
                    }
                }

                new BaseRepository<ProjectInfo>().Update(u => u.RFQNo==rfqno, u => new ProjectInfo
                {
                    Status="6",  //申请签核完毕 等待审批流程结束

                });
                //创建审批流程
                FlowForm form = new FlowForm();
                FlowDetail detail = new FlowDetail();

                form.FLOWID = "1";
                form.FLOWNODE = 1;
                form.CREATEUSER = name;
                form.CREATEDATE = DateTime.Now;
                form.FLOWSTATUS = 1; //等待manager签核
                form.NODENAME = "采购主管";
                form.RFQNO = rfqno;

                detail.FLOWID = "1";
                detail.FLOWNODE = 1;
                detail.NODENAME = "采购主管";
                detail.RFQNO = rfqno;
                detail.AUDITSTATUS = 0; //0待签核 1签核通过 2签核退回

                new BaseRepository<FlowForm>().Add(form);
                new BaseRepository<FlowDetail>().Add(detail);
                context.Response.Write(JsonConvert.SerializeObject(new { code = 0, msg = "提交审批成功！" }));

            }
            catch (Exception ex)
            {
                context.Response.Write(JsonConvert.SerializeObject(new { code = 1, msg = "提交失败：" + ex.Message }));
            }
            

        }

        /// <summary>
        /// 检测报价单是否都已完成
        /// </summary>
        /// <param name="rfqno">RFQ编号</param>
        public void CheckQuoStatus(string rfqno)
        {
            try
            {
                if (string.IsNullOrEmpty(rfqno))
                {
                    return; // 如果RFQ编号为空，直接返回
                }

                var quolist = new BaseRepository<QuoStatus>().Find(u => u.RFQ_NO == rfqno);
                if (quolist == null)
                {
                    return; // 如果没有找到报价列表，直接返回
                }

                var projectinfo = new BaseRepository<ProjectInfo>().FindSingle(u => u.RFQNo == rfqno);
                if (projectinfo == null)
                {
                    return; // 如果没有找到项目信息，直接返回
                }

                // 检查所有报价是否都已完成（状态为1拒绝报价或3已报价）
                foreach (var quo in quolist)
                {
                    if (quo.QUO_STATUS != 1 && quo.QUO_STATUS != 3)
                    {
                        return; // 如果有未完成的报价，直接返回
                    }
                }

                // 如果项目状态为"4"（发送报价结束），则更新为"5"（报价完成）
                if (projectinfo.Status == "4")
                {
                    new BaseRepository<ProjectInfo>().Update(u => u.RFQNo == rfqno, u => new ProjectInfo
                    {
                        Status = "5",   //更新状态 报价完成
                    });
                }
            }
            catch (Exception ex)
            {
                // 记录异常但不抛出，避免影响主流程
                // 可以考虑添加日志记录
            }
        }


        private string GetContentType(string fileExtension)
        {
            switch (fileExtension.ToLower())
            {
                case ".pdf": return "application/pdf";
                case ".doc": return "application/msword";
                case ".docx": return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
                case ".xls": return "application/vnd.ms-excel";
                case ".xlsx": return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
                case ".png": return "image/png";
                case ".jpg":
                case ".jpeg": return "image/jpeg";
                case ".gif": return "image/gif";
                case ".zip": return "application/zip";
                case ".rar": return "application/x-rar-compressed";
                case ".txt": return "text/plain";
                default: return "application/octet-stream";
            }
        }

        private void ResponseError(HttpContext context, string message)
        {
            context.Response.ContentType = "application/json";
            var response = new { code = 500, msg = message, data = new List<object>() };
            var serializer = new JavaScriptSerializer();
            context.Response.Write(serializer.Serialize(response));
        }

        /// <summary>
        /// 从文件路径中提取文件名，添加空值检查
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>文件名，如果路径为空则返回空字符串</returns>
        private string ExtractFileName(string filePath)
        {
            if (string.IsNullOrEmpty(filePath))
            {
                return "";
            }

            try
            {
                return Path.GetFileName(filePath) ?? "";
            }
            catch (Exception)
            {
                return ""; // 如果路径格式错误，返回空字符串
            }
        }

        public void DownloadTemplate(HttpContext context)
        {
            try
            {
                // 获取报价单号
                string quoteNo = context.Request.QueryString["quoteNo"];
                if (string.IsNullOrEmpty(quoteNo))
                {
                    ResponseError(context, "报价单号不能为空");
                    return;
                }

                // 获取相关记录
                var records = new BaseRepository<QuoRecord>().Find(u => u.QUO_NO == quoteNo);
                if (records == null || !records.Any())
                {
                    ResponseError(context, "未找到相关报价记录");
                    return;
                }

                // 使用现有方法生成Excel文件
                string filePath = CreateExcel(records, quoteNo);
                if (string.IsNullOrEmpty(filePath) || !File.Exists(filePath))
                {
                    ResponseError(context, "生成报价模板失败");
                    return;
                }

                // 获取文件信息
                FileInfo fileInfo = new FileInfo(filePath);

                // 设置响应头
                context.Response.Clear();
                context.Response.ClearHeaders();
                context.Response.ClearContent();
                context.Response.AddHeader("Content-Type", GetContentType(fileInfo.Extension));
                context.Response.AddHeader("Content-Disposition", $"attachment; filename*=UTF-8''{HttpUtility.UrlEncode("报价模板_" + quoteNo + fileInfo.Extension)}");
                context.Response.AddHeader("Content-Length", fileInfo.Length.ToString());

                // 输出文件内容
                using (FileStream fs = new FileStream(filePath, FileMode.Open, FileAccess.Read))
                {
                    byte[] buffer = new byte[64 * 1024]; // 64KB buffer
                    int read;
                    while ((read = fs.Read(buffer, 0, buffer.Length)) > 0)
                    {
                        context.Response.OutputStream.Write(buffer, 0, read);
                    }
                    context.Response.Flush();
                }
            }
            catch (Exception ex)
            {
                ResponseError(context, "下载报价模板失败: " + ex.Message);
            }
        }

        public void SaveQuote(HttpContext context)
        {
            dynamic d = new ExpandoObject();
            try
            {
                // 读取请求体内容
                string requestData;
                using (var reader = new StreamReader(context.Request.InputStream))
                {
                    requestData = reader.ReadToEnd();
                }
                
                if (string.IsNullOrEmpty(requestData))
                {
                    // 尝试从表单获取数据
                    requestData = context.Request.Form["data"];
                }
                
                if (string.IsNullOrEmpty(requestData))
                {
                    d.code = 1;
                    d.msg = "未接收到数据";
                    context.Response.Write(JsonConvert.SerializeObject(d));
                    return;
                }
                
                // 解析JSON数据
                var quoteData = JsonConvert.DeserializeObject<List<dynamic>>(requestData);
                
                using (var db = new RFQDBContext())
                {
                    foreach(var item in quoteData)
                    {
                        string id = item.Id.ToString();
                        var record = db.QuoRecords.Find(id);
                        
                        if(record != null)
                        {
                            record.QUO_MFR = item.QUO_MFR?.ToString();
                            record.QUO_MPN = item.QUO_MPN?.ToString();
                            record.ALTER_MPN = item.ALTER_MPN?.ToString();
                            record.QUO_CUR = item.QUO_CUR?.ToString();

                            // 处理数值类型UpdateQuoteStatus
                            if (decimal.TryParse(item.QUO_PRICE?.ToString(), out decimal quoPrice))
                                record.QUO_PRICE = quoPrice;
                            
                            
                            if (decimal.TryParse(item.QUO_USPRICE?.ToString(), out decimal quoUSPrice))
                                record.QUO_USPRICE = quoUSPrice;
                            
                            if (int.TryParse(item.SPQ?.ToString(), out int spq))
                                record.SPQ = spq;
                            else
                                record.SPQ = 0;

                            if (int.TryParse(item.MOQ?.ToString(), out int moq))
                                record.MOQ = moq;
                            else
                                record.MOQ = 0;

                            if (int.TryParse(item.LT?.ToString(), out int lt))
                                record.LT = lt;
                            else
                                record.LT = 0;

                            if (int.TryParse(item.TOOL_COST?.ToString(), out int toolCost))
                                record.TOOL_COST = toolCost;
                            else
                                record.TOOL_COST = 0;

                            record.TOOL_CURR = item.TOOL_CURR?.ToString();
                        }
                    }
                    db.SaveChanges();
                }
                d.code = 0;
                d.msg = "保存成功";
            }
            catch(Exception ex)
            {
                d.code = 1;
                d.msg = ex.Message;
            }
            context.Response.Write(JsonConvert.SerializeObject(d));
        }

        public void ParseQuoteFile(HttpContext context)
        {
            dynamic d = new ExpandoObject();
            try
            {
                var file = context.Request.Files[0];
                if (file != null)
                {
                    // 读取 Excel 文件内容
                    using (var excelPack = new ExcelPackage(file.InputStream))
                    {
                        var quono = context.Request.Params["quoteNo"];
                        
                        var ws = excelPack.Workbook.Worksheets[0];
                        int rowCount = ws.Dimension.Rows;

                        // 结果列表
                        List<object> parsedData = new List<object>();
                        
                        // 从第二行开始读取数据（跳过表头）
                        using (var db = new RFQDBContext())
                        {
                            var existingRecords = db.QuoRecords.Where(u => u.QUO_NO == quono).ToList();
                            
                            for (int row = 2; row <= rowCount; row++)
                            {
                                // 读取ID
                                string id = ws.Cells[row, 1].Value?.ToString();
                                if (string.IsNullOrWhiteSpace(id)) continue;
                                
                                // 查找对应的记录以获取不变的数据
                                var existingRecord = existingRecords.FirstOrDefault(r => r.Id == id);
                                if (existingRecord == null) continue;
                                
                                // 创建数据对象，组合现有数据和Excel数据
                                dynamic rowData = new ExpandoObject();
                                rowData.Id = id;
                                rowData.QUO_NO = existingRecord.QUO_NO;
                                rowData.CUST_PARTNUMBER = existingRecord.CUST_PARTNUMBER;
                                rowData.BOM_DESC = existingRecord.BOM_DESC;
                                rowData.MATERIAL_GROUP = existingRecord.MATERIAL_GROUP;
                                rowData.QUO_SUPP = existingRecord.QUO_SUPP;
                                
                                // 从Excel中读取的数据
                                rowData.QUO_MFR = ws.Cells[row, 7].Value?.ToString();
                                rowData.QUO_MPN = ws.Cells[row, 8].Value?.ToString();
                                rowData.ALTER_MPN = ws.Cells[row, 9].Value?.ToString();
                                rowData.QUO_CUR = ws.Cells[row, 10].Value?.ToString()?.Trim();
                                
                                decimal unitPrice = 0;
                                if (decimal.TryParse(ws.Cells[row, 11].Value?.ToString(), out unitPrice))
                                    rowData.QUO_PRICE = unitPrice;
                                else
                                    rowData.QUO_PRICE = 0;
                                    
                                decimal unitPriceUS = 0;
                                if (decimal.TryParse(ws.Cells[row, 12].Value?.ToString(), out unitPriceUS))
                                    rowData.QUO_USPRICE = unitPriceUS;
                                else
                                    rowData.QUO_USPRICE = 0;
                                    
                                rowData.SPQ = ws.Cells[row, 13].Value?.ToString();
                                rowData.MOQ = ws.Cells[row, 14].Value?.ToString();
                                rowData.LT = ws.Cells[row, 15].Value?.ToString();
                                rowData.TOOL_COST = ws.Cells[row, 16].Value?.ToString();
                                rowData.TOOL_CURR = ws.Cells[row, 17].Value?.ToString();
                                
                                parsedData.Add(rowData);
                            }
                        }
                        
                        d.code = 0;
                        d.msg = "文件解析成功";
                        d.data = parsedData;
                    }
                }
                else
                {
                    d.code = -1;
                    d.msg = "没有接收到文件";
                }   
            }
            catch (Exception ex)
            {
                d.code = -1;
                d.msg = "解析文件失败: " + ex.Message;
            }
            context.Response.Write(JsonConvert.SerializeObject(d));
        }

        public bool IsReusable
        {
            get
            {
                return false;
            }
        }
    }
}