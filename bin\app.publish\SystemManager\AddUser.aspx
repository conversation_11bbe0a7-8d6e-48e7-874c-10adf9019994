﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="AddUser.aspx.cs" Inherits="WebApplication1.SystemManager.AddUser" %>

<!DOCTYPE html>

<head>
<meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../lib/layui-v2.9.21/css/layui.css" media="all">
    <link rel="stylesheet" href="../css/public.css" media="all">
    <title></title>
</head>
<body>
    <form class="layui-form" lay-filter="addform">
    <div class="layuimini-main">
        <style>
            .mylabel {
                width: 180px !important;
            }
        </style>
        <div class="layui-form layuimini-form">
            <%--<div class="layui-form-item">
                    <label class="layui-form-label" style="width: 180px !important">RFQ编号</label>
                    <div class="layui-input-inline">
                        <select name="RFQNo" id="rfqno" lay-search="">
                            <option value="">直接选择或搜索选择</option>
                        </select>
                        (若不选择会自动创建新的)
                    </div>
                </div>--%>
            <div class="layui-form-item ">
                <label class="layui-form-label required" style="width: 180px !important">用户名</label>
                <div class="layui-input-inline">
                    <input type="text" name="UserName" lay-verify="required"  value="" class="layui-input">
                    
                </div>
            </div>
            <div class="layui-form-item ">
                <label class="layui-form-label required" style="width: 180px !important">密码</label>
                <div class="layui-input-inline">
                    <input type="text" name="UserPassword" lay-verify="required"  value="" class="layui-input">
                    
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label required" style="width: 180px !important">用户权限</label>
                <div class="layui-input-inline">
                        <select name="UserRole" id="UserRole" >
                            <option value="">请选择权限</option>
                            <option value="内部用户">内部用户</option>
                            <option value="供应商">供应商</option>
                            <option value="系统管理员">系统管理员</option>
                        </select>
                    </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label required" style="width: 180px !important">流程权限</label>
                <div class="layui-input-inline">
                    <select name="FlowRole" id="FlowRole" >
                            <option value="">请选择权限</option>
                            <option value="采购经理">采购经理</option>
                            <option value="采购主管">采购主管</option>
                            <option value="采购工程师">采购工程师</option>
                            <option value="管理员">管理员</option>
                        </select>
                </div>
            </div>
            
            
           

            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn layui-btn-normal" lay-submit lay-filter="saveBtn">确认保存</button>
                </div>
            </div>
        </div>
    </div>
</form>
    <script src="../lib/layui-v2.9.21/layui.js" charset="utf-8"></script>
</body>

