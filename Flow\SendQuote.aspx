﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="SendQuote.aspx.cs" Inherits="WebApplication1.Flow.SendQuote" %>

<!DOCTYPE html>


<head>
    <meta charset="utf-8">
    <title>发送报价</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../lib/layui-v2.9.21/css/layui.css" media="all">
    <link rel="stylesheet" href="../css/public.css" media="all">
</head>
    

<body>
    <div class="container">
    <div class="layui-container">
        
        <div id="bom-groups"></div>
        <button class="layui-btn" id="nextStep">发送报价</button>
    </div>
        </div>

    <script src="../lib/layui-v2.9.21/layui.js" charset="utf-8"></script>
    <script src="../js/navigate.js" charset="utf-8"></script>

    <script>
    layui.use(['element', 'form', 'layer'], function(){
        var element = layui.element;
        var form = layui.form;
        var layer = layui.layer;
        var $ = layui.jquery;

        function getQueryParam(name) {
            // 保留原有的参数获取逻辑
            var hash = window.location.hash;
            var hashParams = hash.substring(hash.indexOf('?') + 1);
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
            var r = hashParams.match(reg);

            if (r != null) {
                return decodeURIComponent(r[2]);
            }

            var queryParams = window.location.search.substr(1);
            r = queryParams.match(reg);
            if (r != null) {
                return decodeURIComponent(r[2]);
            }

            return null;
        }

        // 从URL获取RFQNo
        var RFQNo = getQueryParam('RFQNo');
        console.log(RFQNo);

        // 添加遮罩层的loading
        var loadingIndex = layer.load();
        // 从后台获取BOM数据和分组规则
        function fetchBOMData() {
            
            return $.ajax({
                url: '../ashx/QuoteControl.ashx?action=QueryBom&RFQNo=' + RFQNo,
                type: 'POST',
                dataType: 'json'
            });
        }

        // 从后台获取供应商列表
        function fetchSuppliers() {
            
            return $.ajax({
                url: '../ashx/QuoteControl.ashx?action=QuerySuppGroup',
                type: 'POST',
                dataType: 'json'
            });
        }

        // 渲染BOM数据分组
        function renderBOMGroups(bomData, groupRules, suppliers) {
            var groupedData = {};
            // 定义要排除的字段列表
            const excludedFields = ['Id', 'COLOR_PARTNUMBER', 'COLOR_CUST', 'COLOR_MANUFACTURER', 'COLOR_MPN'];
            //bomData.forEach(item => {
            //    var group = groupRules.find(rule => item.CUST_PARTNUMBER.startsWith(rule.MATERIALBEGIN));
            //    if (group) {
            //        if (!groupedData[group.GROUP]) {
            //            groupedData[group.GROUP] = [];
            //        }
            //        groupedData[group.GROUP].push(item);
            //    }
            //});
            bomData.forEach(item => {
                if (!groupedData[item.MATERIAL_GROUP]) {
                    groupedData[item.MATERIAL_GROUP] = [];
                }
                groupedData[item.MATERIAL_GROUP].push(item);
            });


            var html = `
            <style>
            .table-wrapper {
                position: relative;
                margin-bottom: 15px;
            }
            .table-container {
                width: 100%;
                overflow: auto;
                max-height: 400px; /* 设置最大高度，超出时显示滚动条 */
            }
            .table-container table {
                min-width: 100%;
                border-collapse: separate;
                border-spacing: 0;
            }
            .table-container thead th {
                position: sticky;
                top: 0;
                background-color: #f2f2f2;
                z-index: 2;
                border-bottom: 1px solid #e6e6e6;
            }
            .table-container th, .table-container td {
                padding: 8px;
                border: 1px solid #e6e6e6;
                white-space: nowrap; /* 防止内容换行 */
            }
            /* 设置滚动条样式 */
            .table-container::-webkit-scrollbar {
                width: 8px;
                height: 8px;
            }
            .table-container::-webkit-scrollbar-track {
                background: #f1f1f1;
            }
            .table-container::-webkit-scrollbar-thumb {
                background: #888;
                border-radius: 4px;
            }
            .table-container::-webkit-scrollbar-thumb:hover {
                background: #555;
            }
            /* 供应商相关样式 */
            .supplier-section {
                margin: 15px 0;
            }
            .supplier-header {
                display: flex;
                align-items: center;
                margin-bottom: 10px;
            }
            .supplier-collapse {
                margin-top: 10px;
                padding: 10px;
                border: 1px solid #e6e6e6;
                display: none;
            }
            .supplier-collapse.show {
                display: block;
            }
            .supplier-toggle {
                margin-left: 10px;
                color: #1E9FFF;
                cursor: pointer;
            }
            .supplier-checkbox-group {
                display: flex;
                flex-wrap: wrap;
                gap: 10px;
            }
            .supplier-checkbox-item {
                min-width: 200px;
            }
            /* 调整折叠面板样式 */
            .layui-colla-content {
                padding: 10px;
            }
            .layui-colla-title {
                background-color: #f8f8f8;
            }
        </style>
            `;
            Object.keys(groupedData).forEach(groupName => {
                var groupData = groupedData[groupName];
                //var rule = groupRules.find(r => r.GROUP === groupName);
                
                var matchingSuppliers = suppliers.filter(s => s.MATERIAL_GROUP === groupName);
                //var matchingSuppliers = suppliers.filter(s =>
                //    s.MATERIAL_GROUP === rule.MATERIALBEGIN ||
                //    groupData.some(item =>
                //        item.CUST_PARTNUMBER &&
                //        typeof item.CUST_PARTNUMBER === 'string' &&
                //        item.CUST_PARTNUMBER.substring(0, 2) === s.MATERIAL_GROUP
                //    )
                //);
                console.log(matchingSuppliers);
                var allKeys = Array.from(new Set(groupData.flatMap(Object.keys)))
                    .filter(key => !excludedFields.includes(key));
                html += `
            <div class="layui-collapse">
            <div class="layui-colla-item">
                <h2 class="layui-colla-title">${groupName} (${groupData.length}条数据)</h2>
                <div class="layui-colla-content">
                    <div class="table-wrapper">
                        <div class="table-container">
                            <table class="layui-table" lay-size="sm">
                                <thead>
                                    <tr>
                                        ${allKeys.map(key => `<th>${key}</th>`).join('')}
                                    </tr>
                                </thead>
                                <tbody>
                                    ${groupData.map(item => `
                                        <tr>
                                            ${allKeys.map(key => `<td>${item[key] || ''}</td>`).join('')}
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
                    <div class="supplier-section">
                        <div class="supplier-header">
                            <div class="layui-form">
                                <input type="checkbox" lay-skin="primary"
                                    lay-filter="selectAll_${groupName}" 
                                    id="selectAll_${groupName}" 
                                    title="全选供应商" 
                                    checked>
                            </div>
                            <div class="supplier-toggle" onclick="toggleSuppliers('${groupName}')">
                                展开供应商列表 (${matchingSuppliers.length})
                            </div>
                        </div>
                        
                        <div class="supplier-collapse" id="supplierCollapse_${groupName}">
                            <div class="layui-form supplier-checkbox-group">
                                ${matchingSuppliers.map(supplier => `
                                    <div class="supplier-checkbox-item">
                                        <input type="checkbox" 
                                            name="supplier_${groupName}" 
                                            value="${supplier.Id || ''}" 
                                            title="${supplier.SUPP_NAME || ''}" 
                                            lay-skin="primary"
                                            checked
                                            lay-filter="supplier_${groupName}">
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>

        `;
            });

            $('#bom-groups').html(html);
            element.render('collapse');
            form.render('checkbox');

            // 为每个分组添加全选事件监听
            Object.keys(groupedData).forEach(groupName => {
                form.on(`checkbox(selectAll_${groupName})`, function (data) {
                    $(`input[name="supplier_${groupName}"]`).each(function () {
                        $(this).prop('checked', data.elem.checked);
                    });
                    form.render('checkbox');
                });

                // 监听单个供应商选择变化
                form.on(`checkbox(supplier_${groupName})`, function () {
                    var allChecked = true;
                    $(`input[name="supplier_${groupName}"]`).each(function () {
                        if (!$(this).prop('checked')) {
                            allChecked = false;
                            return false;
                        }
                    });
                    $(`#selectAll_${groupName}`).prop('checked', allChecked);
                    form.render('checkbox');
                });
            });
        }
        // 添加供应商列表展开/收起功能
        window.toggleSuppliers = function (groupName) {
            var $collapse = $(`#supplierCollapse_${groupName}`);
            var $toggle = $collapse.siblings('.supplier-header').find('.supplier-toggle');

            if ($collapse.hasClass('show')) {
                $collapse.removeClass('show');
                $toggle.text(`展开供应商列表 (${$collapse.find('.supplier-checkbox-item').length})`);
            } else {
                $collapse.addClass('show');
                $toggle.text(`收起供应商列表 (${$collapse.find('.supplier-checkbox-item').length})`);
            }
        };

        // 初始化页面
        Promise.all([fetchBOMData(), fetchSuppliers()]).then(([bomResult, suppliersResult]) => {
            
            if (bomResult.code === 0 && suppliersResult.code === 0) {
                renderBOMGroups(bomResult.bomdata, bomResult.groupdata, suppliersResult.data);
            } else {
                layer.msg('获取数据失败，请重试');
            }
        }).catch(error => {
            layer.msg('获取数据失败，请重试');
            console.error('Error:', error);
        })
            .finally(() => {
                // 无论成功失败，都关闭loading
                layer.close(loadingIndex);
            });;

        // 下一步按钮点击事件
        $('#nextStep').on('click', function () {
            var selectedSuppliers = {};

            // 获取所有分组的复选框值
            $('[name^="supplier_"]').each(function () {
                var groupName = $(this).attr('name').split('_')[1];
                if (!selectedSuppliers[groupName]) {
                    selectedSuppliers[groupName] = [];
                }
                if ($(this).prop('checked')) {
                    selectedSuppliers[groupName].push($(this).val());
                }
            });
            console.log(selectedSuppliers);
            // 检查每个分组是否至少选择了一个供应商
            var allGroupsHaveSelection = Object.values(selectedSuppliers).every(group => group.length > 0);

            if (allGroupsHaveSelection) {
                $.ajax({
                    url: '../ashx/QuoteControl.ashx?action=SendQuote',
                    type: 'POST',
                    data: { suppliers: JSON.stringify(selectedSuppliers) },
                    success: function (res) {
                        //var res = JSON.parse(response);
                        if (res.code === 0) {
                            layer.msg('发送报价成功！');
                            //window.location = "index.html#/DataManager/QueryQuote.aspx";
                            navigateInIframe("../DataManager/QueryQuote.aspx?RFQNo=" + RFQNo, "查询供应商报价");
                        } else {
                            layer.msg('发送失败：' + res.msg);
                        }
                    },
                    error: function () {
                        layer.msg('发送失败，请重试');
                    }
                });
            } else {
                layer.msg('请为每个组别至少选择一个供应商');
            }
        });
    });
    </script>
</body>

