﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="QuoteDetail.aspx.cs" Inherits="WebApplication1.Supplier.QuoteDetail" %>


<head runat="server">
    <meta charset="utf-8" />
    <title>报价单详情</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../lib/layui-v2.9.21/css/layui.css" media="all">
    <link rel="stylesheet" href="../css/public.css" media="all">
    <style>
        body {
            background-color: #f2f2f2;
            padding: 20px;
        }
        .quote-detail-container {
            background-color: #fff;
            padding: 20px;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .quote-header {
            margin-bottom: 20px;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="quote-detail-container">
        <div class="quote-header">
            <h2>报价记录详情 - <span id="quotationNo"></span></h2>
        </div>
        <table id="quoteDetailTable" lay-filter="quoteDetailTable"></table>
        
        
    </div>
      <script src="../lib/layui-v2.9.21/layui.js" charset="utf-8"></script>
    <script>
        layui.use(['table', 'jquery'], function () {
            var table = layui.table;
            var $ = layui.$;

            // 从URL获取quotationNo
            var quotationNo = getQueryParam('quotationNo');
            $('#quotationNo').text(quotationNo);

            // 渲染表格
            table.render({
                elem: '#quoteDetailTable',
                url: '../ashx/QuoteControl.ashx?action=QueryRecord',
                where: { quoteNo: quotationNo },
                limit: 10000,
                cols: [[
                    { field: 'MATERIAL_GROUP', title: '物料组别', width: 150 },
                    { field: 'CUST_PARTNUMBER', title: '客户料号', width: 150 },
                    { field: 'BOM_DESC', title: '料号描述', width: 250 },
                    { field: 'QUO_MFR', title: 'MFR', width: 150 },
                    { field: 'QUO_MPN', title: 'MPN', width: 150 },
                    { field: 'ALTER_MPN', title: 'ALTER_MPN', width: 100 },
                    { field: 'QUO_CUR', title: '报价币别', width: 100 },
                    { field: 'QUO_SUPP', title: '供应商', width: 100 },
                    { field: 'QUO_PRICE', title: '单价', width: 100 },
                    { field: 'QUO_USPRICE', title: 'US单价', width: 100 },
                    { field: 'ORDER_MIN', title: '最小订货量', width: 100 },
                    { field: 'SPQ', title: 'SPQ', width: 100 },
                    { field: 'MOQ', title: 'MOQ', width: 100 },
                    { field: 'LT', title: 'LT', width: 100 },
                    { field: 'TOOL_COST', title: 'TOOL_COST', width: 150 },
                    { field: 'TOOL_CURR', title: 'TOOL_CURR', width: 100 },
                    { field: 'CREATE_DATE', title: '报价时间', width: 150 },
                ]]
                
            });

            

            // 获取URL参数的辅助函数
            function getQueryParam(name) {
                var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
                var r = window.location.search.substr(1).match(reg);
                if (r != null) return decodeURIComponent(r[2]);
                return null;
            }
        });
    </script>
</body>

