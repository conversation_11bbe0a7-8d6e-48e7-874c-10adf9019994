﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="AddQuote.aspx.cs" Inherits="WebApplication1.Supplier.AddQuote" %>

<!DOCTYPE html>
<style>
    /* 确保表格容器正常显示 */
    .layui-table-box {
        width: 100% !important;
    }
    
    /* 处理表格滚动 */
    .layui-table-body {
        overflow: auto !important;
    }
    
    /* 确保表格列表头固定 */
    .layui-table-header {
        overflow: hidden !important;
    }
    
    /* 设置合适的内边距 */
    #bomDialog .layui-card-body {
        padding: 10px;
    }
    
    /* 文件名称显示样式 */
    .file-info {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    
    .file-name {
        max-width: 120px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-right: 5px;
        display: inline-block;
        vertical-align: middle;
    }
    
    /* 文件图标样式 */
    .file-icon {
        margin-right: 5px;
        color: #1E9FFF;
    }
    
    /* 美化按钮样式 */
    .layui-btn-custom {
        padding: 0 12px;
        margin: 0 2px;
    }
    
    /* 美化表格样式 */
    .layui-table-view {
        margin: 10px 0;
    }
    
    /* 操作按钮组样式 */
    .quote-btn-group {
        white-space: nowrap;
    }
    
    .quote-btn-group .layui-btn {
        margin: 2px;
    }
    
    /* 状态标签样式 */
    .status-tag {
        padding: 2px 8px;
        border-radius: 2px;
        color: #fff;
        display: inline-block;
    }
    
    .status-tag-0 { background-color: #FFB800; } /* 未报价 */
    .status-tag-1 { background-color: #FF5722; } /* 已拒绝 */
    .status-tag-2 { background-color: #1E9FFF; } /* 报价中 */
    .status-tag-3 { background-color: #009688; } /* 已报价 */
</style>
<head runat="server">
 <meta charset="utf-8">
    <title>供应商报价</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../lib/layui-v2.9.21/css/layui.css" media="all">
    <link rel="stylesheet" href="../css/public.css" media="all">
</head>
<body>
    <div class="layui-container" style="margin-top: 15px;">
        <div class="layui-card">
            <div class="layui-card-header">供应商报价管理</div>
            <div class="layui-card-body">
                <!-- 数据表格 -->
                <table id="quoteTable" lay-filter="quoteTable"></table>
            </div>
        </div>
    </div>

    <!-- 报价操作按钮模板 -->
    <script type="text/html" id="quoteBtnTpl">
    <div class="quote-btn-group">
    {{#  if(d.QUO_STATUS === 0){ }}
        <button class="layui-btn layui-btn-sm layui-btn-normal layui-btn-custom" lay-event="accept"><i class="layui-icon">&#xe605;</i> 接受</button>
        <button class="layui-btn layui-btn-sm layui-btn-danger layui-btn-custom" lay-event="reject"><i class="layui-icon">&#x1006;</i> 拒绝</button>
    {{#  } else if(d.QUO_STATUS === 2) { }}
        <button class="layui-btn layui-btn-sm layui-btn-warm layui-btn-custom" lay-event="download"><i class="layui-icon">&#xe601;</i> 下载模板</button>
        <button class="layui-btn layui-btn-sm layui-btn-normal layui-btn-custom" lay-event="upload"><i class="layui-icon">&#xe62f;</i> 上传报价</button>
    {{#  } else if(d.QUO_STATUS === 1) { }}
        <span class="status-tag status-tag-1"><i class="layui-icon">&#xe69c;</i> 已拒绝</span>
    {{#  } else if(d.QUO_STATUS === 3) { }}
        <button class="layui-btn layui-btn-sm layui-btn-primary layui-btn-custom" lay-event="viewquote"><i class="layui-icon">&#xe705;</i> 查看报价</button>
    {{#  } }}
    </div>
</script>
<script type="text/html" id="fileUploadTpl">
    <div class="file-info">
        {{# var fileField = 'FILE_UPLOAD' + d.uploadIndex; }}
        {{# if(d[fileField]) { }}
            <div class="file-display">
                <i class="layui-icon file-icon">&#xe655;</i>
                <span class="file-name" title="{{d[fileField]}}">
                    {{d[fileField]}}
                </span>
            </div>
        {{# } }}
        <button class="layui-btn layui-btn-xs layui-btn-normal upload-btn layui-btn-custom" data-index="{{d.uploadIndex}}" data-quote-no="{{d.QUO_NO}}">
            <i class="layui-icon">{{# if(d[fileField]) { }}&#xe642;{{# } else { }}&#xe67c;{{# } }}</i>
            {{# if(d[fileField]) { }}
                重新上传
            {{# } else { }}
                上传文件
            {{# } }}
        </button>
    </div>
</script>

<!-- ALTER_MPN(YES/NO)下拉选择模板 -->
<script type="text/html" id="alterMpnTpl">
    <select name="ALTER_MPN" class="layui-border select-demo-primary" style="height: 36px;" data-value="{{d.ALTER_MPN}}" data-row="{{d.LAY_INDEX}}" lay-ignore>
        <option value="">请选择</option>
        <option value="YES" {{# if(d.ALTER_MPN === 'YES'){ }}selected{{# } }}>YES</option>
        <option value="NO" {{# if(d.ALTER_MPN === 'NO'){ }}selected{{# } }}>NO</option>
    </select>
</script>

<!-- 币别下拉选择模板 -->
<script type="text/html" id="currencyTpl">
    <select name="QUO_CUR" class="layui-border select-demo-primary" style="height: 36px;" data-value="{{d.QUO_CUR}}" data-row="{{d.LAY_INDEX}}"  lay-ignore>
        <option value="">请选择币别</option>
        {{# layui.each(d.currencyOptions || window.currencyOptions || [], function(index, item){ }}
        <option value="{{item.CURRENCY}}" {{# if(d.QUO_CUR === item.CURRENCY){ }}selected{{# } }}>{{item.CURRENCY}}</option>
        {{# }); }}
    </select>
</script>

    <!-- BOM信息弹窗 -->
<div id="bomDialog" style="display: none; padding: 15px; background-color: #fff;">
    <div class="layui-form">
        <div class="layui-form-item">
            <div class="layui-input-block">
                <input type="radio" name="inputMode" value="manual" title="手动填写" lay-filter="inputMode" checked>
                <input type="radio" name="inputMode" value="file" title="文件上传" lay-filter="inputMode">
            </div>
        </div>
    </div>
    
    <!-- 手动填写模式 -->
    <div id="manualMode" style="display: block;">
        <table id="RecordTable" lay-filter="RecordTable"></table>
        <div style="margin-top: 15px; text-align: center;">
            <button type="button" class="layui-btn" id="saveBtn">保存报价</button>
        </div>
    </div>
    
    <!-- 文件上传模式 -->
    <div id="fileMode" style="display: none;">
        <div style="margin-top: 15px; margin-bottom: 15px; text-align: center;">
            <button type="button" class="layui-btn" id="uploadBtn">
                <i class="layui-icon">&#xe67c;</i>上传报价文件
            </button>
        </div>
        <div id="fileDataPreview" style="display: none;">
            <div class="layui-card">
                <div class="layui-card-header">文件数据预览</div>
                <div class="layui-card-body">
                    <table id="fileDataTable" lay-filter="fileDataTable"></table>
                </div>
            </div>
            <div style="margin-top: 15px; text-align: center;">
                <button type="button" class="layui-btn" id="submitFileDataBtn">提交报价</button>
            </div>
        </div>
    </div>
</div>
    </body>

    <script src="../lib/layui-v2.9.21/layui.js" charset="utf-8"></script>
    <script>
        layui.use(['table', 'layer', 'upload', 'form'], function(){
            var table = layui.table;
            var layer = layui.layer;
            var upload = layui.upload;
            var form = layui.form;
            var $ = layui.$;

            // 确保表单渲染
            form.render();
            console.log('初始表单渲染完成');

            // 全局存储币别选项
            window.currencyOptions = [];
            
            // 获取币别数据
            function loadCurrencyOptions() {
                $.ajax({
                    url: '../ashx/ExchangeRate.ashx/getList',
                    type: 'GET',
                    async: false,
                    success: function(res) {
                        if (res.code === 0 && res.data) {
                            window.currencyOptions = res.data;
                            console.log('币别数据加载成功:', window.currencyOptions);
                        } else {
                            console.error('获取币别数据失败:', res.msg);
                        }
                    },
                    error: function(err) {
                        console.error('币别数据请求失败:', err);
                    }
                });
            }
            
            // 加载币别数据
            loadCurrencyOptions();
            
            // 监听ALTER_MPN和币别下拉选择事件
            $(document).on('change', 'select[name="ALTER_MPN"], select[name="QUO_CUR"]', function() {
                var $select = $(this);
                var rowIndex = $select.data('row');
                var field = $select.attr('name');
                var value = $select.val();
                
                console.log('Select changed:', {
                    rowIndex: rowIndex,
                    field: field,
                    value: value
                });

                // 确定当前操作的表格
                var $container = $select.closest('.layui-table-view');
                var tableId;
                
                

                // 如果还是找不到，通过上下文判断
                if (!tableId) {
                    if ($select.closest('#fileMode').length > 0) {
                        tableId = 'fileDataTable';
                    } else {
                        tableId = 'RecordTable';
                    }
                    console.log('Determined table ID by context:', tableId);
                }

                // 确保表格缓存存在
                if (!table.cache[tableId]) {
                    console.log('Initializing table cache for:', tableId);
                    table.cache[tableId] = [];
                }
                
                // 获取当前行数据
                var rowData = table.cache[tableId][rowIndex];
                
                if (rowData) {
                    // console.log('Before update:', {
                    //     tableId: tableId,
                    //     rowIndex: rowIndex,
                    //     field: field,
                    //     oldValue: rowData[field],
                    //     newValue: value
                    // });
                    
                    // 更新数据
                    rowData[field] = value;
                    // 确保更新后的数据被保存回缓存
                    table.cache[tableId][rowIndex] = rowData;
                    
                    // console.log('After update:', {
                    //     tableId: tableId,
                    //     rowIndex: rowIndex,
                    //     field: field,
                    //     value: table.cache[tableId][rowIndex][field]
                    // });
                    
                    // 手动更新表格中对应单元格的显示
                    var $td = $select.closest('td');
                    if ($td.length > 0) {
                        $td.find('select').val(value);
                    }
                } else {
                    console.error('Row data not found:', {
                        tableId: tableId,
                        rowIndex: rowIndex,
                        cacheExists: !!table.cache[tableId],
                        cacheLength: table.cache[tableId] ? table.cache[tableId].length : 0
                    });
                }
            });

            // 监听表格单元格编辑
            table.on('edit(RecordTable)', function(obj){
                var field = obj.field;
                var value = obj.value;
                
                // 价格字段验证
                if (field === 'QUO_PRICE' || field === 'QUO_USPRICE' || field === 'SPQ' || field === 'MOQ' || field === 'LT' || field === 'TOOL_COST') {
                    // 判断是否为有效数字
                    if (isNaN(value) || value === '') {
                        layer.msg('请输入有效的数字');
                        // 清空值
                        obj.update({
                            [field]: ''
                        });
                        return;
                    }
                    
                    // 确保是正数
                    if (parseFloat(value) < 0) {
                        layer.msg('请输入大于等于0的数字');
                        obj.update({
                            [field]: ''
                        });
                        return;
                    }
                }
            });
            
            // 监听预览表格单元格编辑
            table.on('edit(fileDataTable)', function(obj){
                var field = obj.field;
                var value = obj.value;
                
                // 价格字段验证
                if (field === 'QUO_PRICE' || field === 'QUO_USPRICE' || field === 'SPQ' || field === 'MOQ' || field === 'LT' || field === 'TOOL_COST') {
                    // 判断是否为有效数字
                    if (isNaN(value) || value === '') {
                        layer.msg('请输入有效的数字');
                        // 清空值
                        obj.update({
                            [field]: ''
                        });
                        return;
                    }
                    
                    // 确保是正数
                    if (parseFloat(value) < 0) {
                        layer.msg('请输入大于等于0的数字');
                        obj.update({
                            [field]: ''
                        });
                        return;
                    }
                }
            });

            // 模式切换
            form.on('radio(inputMode)', function(data){
                console.log('Mode changed to:', data.value); // 添加调试日志
                if(data.value === 'manual') {
                    $('#manualMode').show();
                    $('#fileMode').hide();
                } else {
                    $('#manualMode').hide();
                    $('#fileMode').show();
                }
            });

            // 添加下载报价模板函数
            function downloadQuoteTemplate(quoteNo) {
                // 显示加载提示
                var loadIndex = layer.load(1, {
                    shade: [0.1, '#fff']
                });
                
                // 创建下载链接
                var downloadUrl = '../ashx/QuoteControl.ashx?action=DownloadTemplate&quoteNo=' + quoteNo;
                
                // 创建一个隐形的a元素
                var link = document.createElement('a');
                link.style.display = 'none';
                link.href = downloadUrl;
                link.setAttribute('download', '报价模板_' + quoteNo + '.xlsx'); // 设置下载文件名
                
                // 添加到body
                document.body.appendChild(link);
                
                // 模拟点击
                link.click();
                
                // 移除元素
                setTimeout(function() {
                    document.body.removeChild(link);
                    layer.close(loadIndex); // 关闭加载提示
                }, 100);
            }

            // 添加一个变量跟踪文件上传状态
            var hasFileUploaded = false;

            // 记录上传实例
            var uploadInstance = null;

            // 保存报价数据
            $('#saveBtn').on('click', function(){
                var data = table.cache.RecordTable;
                console.log('Saving data:', data);
                
                if(!data) {
                    layer.msg('没有需要保存的数据');
                    return;
                }
                
                // 验证数据 - 检查所有必填字段
                var hasError = false;
                var errorMsg = '';
                
                // 定义所有需要验证的字段及其中文名称
                var requiredFields = {
                    'QUO_NO': '报价单号',
                    'CUST_PARTNUMBER': '客户料号',
                    'BOM_DESC': '物料描述',
                    'MATERIAL_GROUP': '物料组',
                    'QUO_MFR': 'MFR',
                    'QUO_MPN': 'MPN',
                    'ALTER_MPN': 'ALTER_MPN',
                    'QUO_CUR': '报价币别',
                    'QUO_PRICE': '报价单价',
                    'QUO_USPRICE': '报价单价(美元)',
                    'SPQ': 'SPQ',
                    'MOQ': 'MOQ',
                    'LT': 'LT',
                    'TOOL_COST': 'TOOL_COST',
                    'TOOL_CURR': 'TOOL_CURR'
                };
                
                // 遍历每一行数据进行验证
                for (var i = 0; i < data.length; i++) {
                    var row = data[i];
                    
                    // 检查每个必填字段
                    for (var field in requiredFields) {
                        var value = row[field];
                        // 检查值是否为空、null、undefined或纯空格
                        if (value === null || value === undefined || String(value).trim() === '') {
                            hasError = true;
                            errorMsg = '第' + (i + 1) + '行的"' + requiredFields[field] + '"不能为空，请填写完整信息';
                            break;
                        }
                    }
                    
                    // 如果发现错误，跳出外层循环
                    if (hasError) {
                        break;
                    }
                }
                
                if (hasError) {
                    layer.msg(errorMsg);
                    return;
                }
                
                $.ajax({
                    url: '../ashx/QuoteControl.ashx?action=SaveQuote',
                    type: 'POST',
                    data: JSON.stringify(data),
                    contentType: 'application/json',
                    success: function (response) {
                        var res = eval("(" + response + ")");
                        if(res.code === 0) {
                            layer.msg('保存成功');
                            table.reload('RecordTable');
                        } else {
                            layer.msg('保存失败：' + res.msg);
                        }
                    }
                });
            });

            // 初始化报价单表格
            table.render({
                elem: '#quoteTable'
                , url: '../ashx/QuoteControl.ashx?action=QuerySelfQuote'
                , cols: [[
                    { field: 'Id', width: 150, title: 'guid', hide: true }
                    ,{ field: 'QUO_NO', title: '报价单号', width: 220 }
                    , { field: 'MATERIAL_GROUP', title: '物料组别', width: 100 }
                    , { field: 'CREATE_DATE', title: '创建日期', width: 120 }
                    , { field: 'QUO_DATE', title: '报价日期', width: 120 }
                    , {
                        field: 'QUO_STATUS', title: '状态', width: 120, templet: function (d) {
                            // 状态码转换为文字和样式
                            var statusText = '';
                            switch (d.QUO_STATUS) {
                                case 0: statusText = '未报价'; break;
                                case 1: statusText = '已拒绝'; break;
                                case 2: statusText = '报价中'; break;
                                case 3: statusText = '已报价'; break;
                                default: statusText = '未知状态';
                            }
                            return '<span class="status-tag status-tag-' + d.QUO_STATUS + '">' + statusText + '</span>';
                        }
                    }
                    // 文件上传列
                    , {
                        field: 'FILE_UPLOAD1', title: '文件1', width: 250, templet: function (d) {
                            d.uploadIndex = 1;
                            return layui.laytpl($('#fileUploadTpl').html()).render(d);
                        }
                    }
                    , {
                        field: 'FILE_UPLOAD2', title: '文件2', width: 250, templet: function (d) {
                            d.uploadIndex = 2;
                            return layui.laytpl($('#fileUploadTpl').html()).render(d);
                        }
                    }
                    , {
                        field: 'FILE_UPLOAD3', title: '文件3', width: 250, templet: function (d) {
                            d.uploadIndex = 3;
                            return layui.laytpl($('#fileUploadTpl').html()).render(d);
                        }
                    }
                    ,{field: 'operation', title: '操作', toolbar: '#quoteBtnTpl', width: 250, fixed: 'right'}
                ]]
                , page: false
                , height: 500
                , limit: 10000
                , skin: 'line' // 表格风格
                , even: true // 开启隔行背景
                , size: 'lg' // 大尺寸
            });

            // 监听表格工具条事件
            table.on('tool(quoteTable)', function (obj) {
                var data = obj.data;
                
                if (obj.event === 'accept') {
                    // 接受报价
                    layer.confirm('确定接受该报价单？', function (index) {
                        $.post('../ashx/QuoteControl.ashx?action=AcceptQuote', {
                            quoteNo: data.QUO_NO
                        }, function (response) {
                            var res = JSON.parse(response);
                            if (res.code === 0) {
                                table.reload('quoteTable');
                            }
                        });
                        layer.close(index);
                    });
                } else if (obj.event === 'reject') {
                    // 拒绝报价
                    layer.confirm('确定拒绝该报价单？', function (index) {
                        $.post('../ashx/QuoteControl.ashx?action=RejectQuote', {
                            quoteNo: data.QUO_NO
                        }, function (response) {
                            var res = JSON.parse(response);
                            if (res.code === 0) {
                                layer.msg('已拒绝报价');
                                table.reload('quoteTable');
                            }
                        });
                        layer.close(index);
                    });
                } else if (obj.event === 'download') {
                    // 下载报价模板
                    downloadQuoteTemplate(data.QUO_NO);
                } else if (obj.event === 'upload' || obj.event === 'viewquote') {
                    // 重置上传状态
                    hasFileUploaded = false;
                    
                    // 打开BOM信息弹窗
                    layer.open({
                        type: 1,
                        title: 'BOM信息',
                        content: $('#bomDialog'),
                        area: ['95%', '90%'],  // 调整弹窗大小
                        maxmin: true,  // 允许最大化
                        shade: 0.3,  // 添加遮罩
                        closeBtn: 1,
                        resize: true,
                        success: function (layero, index) {
                            // 隐藏预览区域（初始状态）
                            $('#fileDataPreview').hide();
                            
                            // 加载BOM表格数据
                            table.render({
                                elem: '#RecordTable',
                                url: '../ashx/QuoteControl.ashx?action=QueryRecord&quoteNo=' + data.QUO_NO,
                                css: [ // 设置单元格样式
                                    '.layui-table-cell{height: auto; line-height: 28px; padding: 6px 15px;}',
                                    '.layui-table-cell .layui-colorpicker{width: 38px; height: 38px;}',
                                    '.layui-table-cell select{height: 38px; padding: 0 5px;}'
                                ].join(''),
                                height: 'full-100',  // 自适应高度
                                id: 'RecordTable', // 明确设置表格ID
                                cols: [[
                                    { field: 'Id', width: 150, title: 'guid', hide: true },
                                    { field: 'QUO_NO', width: 150, title: '报价单号' },
                                    { field: 'CUST_PARTNUMBER', width: 150, title: '客户料号' },
                                    { field: 'BOM_DESC', width: 150, title: '物料描述' },
                                    { field: 'MATERIAL_GROUP', width: 150, title: '物料组' },
                                    { field: 'QUO_MFR', width: 150, title: 'MFR', edit: 'text' },
                                    { field: 'QUO_MPN', width: 150, title: 'MPN', edit: 'text' },
                                    { field: 'ALTER_MPN', width: 150, title: 'ALTER_MPN(YES/NO)', templet: '#alterMpnTpl' },
                                    { field: 'QUO_CUR', width: 150, title: '报价币别', templet: '#currencyTpl' },
                                    { field: 'QUO_PRICE', width: 150, title: '报价单价', edit: 'text', type: 'number' },
                                    { field: 'QUO_USPRICE', width: 150, title: '报价单价(美元)', edit: 'text', type: 'number' },
                                    { field: 'SPQ', width: 150, title: 'SPQ', edit: 'text' , type: 'number'  },
                                    { field: 'MOQ', width: 150, title: 'MOQ', edit: 'text' , type: 'number' },
                                    { field: 'LT', width: 150, title: 'LT', edit: 'text' , type: 'number' },
                                    { field: 'TOOL_COST', width: 150, title: 'TOOL_COST', edit: 'text' , type: 'number' },
                                    { field: 'TOOL_CURR', width: 150, title: 'TOOL_CURR', edit: 'text' }
                                ]],
                                page: false,
                                
                                limit: 10000,
                                skin: 'line',
                                even: true,
                                done: function() {
                                    // 表格渲染完成后，重新渲染表单元素
                                    form.render();
                                    console.log('Table initialized with ID:', this.id);
                                    // 确保表格缓存存在
                                    if (!table.cache['RecordTable']) {
                                        table.cache['RecordTable'] = [];
                                    }
                                }
                            });

                            // 强制重新渲染表单，确保事件绑定生效
                            form.render('radio');
                            
                            // 再次显式绑定切换事件
                            form.on('radio(inputMode)', function(data) {
                                console.log('Mode changed to:', data.value);
                                if(data.value === 'manual') {
                                    $('#manualMode').show();
                                    $('#fileMode').hide();
                                } else {
                                    $('#manualMode').hide();
                                    $('#fileMode').show();
                                }
                            });

                            // 初始化上传按钮
                            upload.render({
                                elem: '#uploadBtn',
                                url: '../ashx/QuoteControl.ashx?action=ParseQuoteFile&quoteNo=' + data.QUO_NO,
                                accept: 'file',
                                exts: 'xls|xlsx',
                                before: function () {
                                    layer.load();
                                },
                                done: function (res) {
                                    layer.closeAll('loading');
                                    if (res.code === 0) {
                                        layer.msg(res.msg);
                                        
                                        // 显示预览区域
                                        $('#fileDataPreview').show();
                                        
                                        // 渲染预览表格
                                        table.render({
                                            elem: '#fileDataTable',
                                            data: res.data,
                                            height: '450px',
                                            id: 'fileDataTable', // 明确设置表格ID
                                            cols: [[
                                                { field: 'Id', width: 150, title: 'guid', hide: true },
                                                { field: 'QUO_NO', width: 150, title: '报价单号' },
                                                { field: 'CUST_PARTNUMBER', width: 150, title: '客户料号' },
                                                { field: 'BOM_DESC', width: 150, title: '物料描述' },
                                                { field: 'MATERIAL_GROUP', width: 150, title: '物料组' },
                                                { field: 'QUO_MFR', width: 150, title: 'MFR', edit: 'text' },
                                                { field: 'QUO_MPN', width: 150, title: 'MPN', edit: 'text' },
                                                { field: 'ALTER_MPN', width: 150, title: 'ALTER_MPN(YES/NO)', templet: '#alterMpnTpl' },
                                                { field: 'QUO_CUR', width: 150, title: '报价币别', templet: '#currencyTpl' },
                                                { field: 'QUO_PRICE', width: 150, title: '报价单价', edit: 'text', type: 'number' },
                                                { field: 'QUO_USPRICE', width: 150, title: '报价单价(美元)', edit: 'text', type: 'number' },
                                                { field: 'SPQ', width: 150, title: 'SPQ', edit: 'text', type: 'number' },
                                                { field: 'MOQ', width: 150, title: 'MOQ', edit: 'text', type: 'number' },
                                                { field: 'LT', width: 150, title: 'LT', edit: 'text', type: 'number' },
                                                { field: 'TOOL_COST', width: 150, title: 'TOOL_COST', edit: 'text', type: 'number' },
                                                { field: 'TOOL_CURR', width: 150, title: 'TOOL_CURR', edit: 'text' }
                                            ]],
                                            page: false,
                                            limit: 10000,
                                            skin: 'line',
                                            even: true,
                                            done: function() {
                                                // 表格渲染完成后，重新渲染表单元素
                                                form.render();
                                                console.log('FileDataTable initialized with ID:', this.id);
                                                // 确保表格缓存存在
                                                if (!table.cache['fileDataTable']) {
                                                    table.cache['fileDataTable'] = [];
                                                }
                                            }
                                        });
                                        
                                        // 绑定提交按钮事件
                                        $('#submitFileDataBtn').off('click').on('click', function() {
                                            var fileData = table.cache.fileDataTable;
                                            if (!fileData || fileData.length === 0) {
                                                layer.msg('没有需要保存的数据');
                                                return;
                                            }

                                            // 新增：验证逻辑
                                            for (var i = 0; i < fileData.length; i++) {
                                                var row = fileData[i];
                                                // 需要验证的字段列表
                                                var fieldsToValidate = ['QUO_NO', 'CUST_PARTNUMBER', 'BOM_DESC', 'MATERIAL_GROUP', 'QUO_MFR', 'QUO_MPN', 'ALTER_MPN', 'QUO_CUR', 'QUO_PRICE', 'QUO_USPRICE', 'SPQ', 'MOQ', 'LT', 'TOOL_COST', 'TOOL_CURR'];
                                                for (var j = 0; j < fieldsToValidate.length; j++) {
                                                    var field = fieldsToValidate[j];
                                                    // 检查值是否为 null, undefined, 或空字符串 (包括去除前后空格后判断)
                                                    if (row[field] === null || row[field] === undefined || String(row[field]).trim() === '') {
                                                        layer.msg('第 ' + (i + 1) + ' 行的字段 "' + field + '" 不能为空，请检查后提交。');
                                                        return false; // 阻止提交
                                                    }
                                                }
                                            }
                                            
                                            // 确认提交
                                            layer.confirm('确定要提交报价数据吗？', function(index) {
                                                $.ajax({
                                                    url: '../ashx/QuoteControl.ashx?action=SaveQuote',
                                                    type: 'POST',
                                                    data: JSON.stringify(fileData),
                                                    contentType: 'application/json',
                                                    success: function(response) {
                                                        var res = JSON.parse(response);
                                                        if (res.code === 0) {
                                                            hasFileUploaded = true;
                                                            
                                                            // 关闭确认弹窗
                                                            layer.close(index);
                                                            
                                                            // 更新报价状态
                                                            $.post('../ashx/QuoteControl.ashx?action=UpdateQuoteStatus', {
                                                                quoteNo: data.QUO_NO
                                                            }, function(response) {
                                                                var res = JSON.parse(response);
                                                                if (res.code === 0) {
                                                                    layer.msg('报价数据保存成功');
                                                                    table.reload('quoteTable');
                                                                    // 关闭BOM信息弹窗
                                                                    layer.closeAll('page');
                                                                }
                                                            });
                                                        } else {
                                                            layer.msg('保存失败：' + res.msg);
                                                        }
                                                    },
                                                    error: function() {
                                                        layer.msg('保存失败，请重试');
                                                    }
                                                });
                                            });
                                        });
                                    } else {
                                        layer.msg('上传失败：' + res.msg);
                                    }
                                },
                                error: function () {
                                    layer.closeAll('loading');
                                    layer.msg('上传失败，请重试');
                                }
                            });
                        },
                        end: function () {
                            // 不再自动弹出确认框，由用户点击提交按钮触发保存流程
                            $('.layui-layer-shade').remove();
                        }
                    });
                }
            });
            
            // 绑定表格中的上传按钮点击事件
            $(document).on('click', '.upload-btn', function() {
                var index = $(this).data('index');
                var quoteNo = $(this).data('quote-no');
                
                // 创建唯一的上传实例ID
                var uploadId = 'fileUpload' + index + '_' + new Date().getTime();
                
                // 创建临时文件上传元素
                var uploadInput = $('<input type="file" id="' + uploadId + '" style="display:none;">');
                $('body').append(uploadInput);
                
                // 初始化上传组件
                upload.render({
                    elem: '#' + uploadId,
                    url: '../ashx/QuoteControl.ashx?action=UploadFile',
                    data: {
                        quoteNo: quoteNo,
                        fileIndex: index
                    },
                    accept: 'file',
                    before: function() {
                        layer.load(1);
                    },
                    done: function(res) {
                        layer.closeAll('loading');
                        if(res.code === 0) {
                            layer.msg('上传成功');
                            // 重新加载表格
                            table.reload('quoteTable');
                        } else {
                            layer.msg('上传失败：' + res.msg);
                        }
                        // 移除临时上传元素
                        $('#' + uploadId).remove();
                    },
                    error: function() {
                        layer.closeAll('loading');
                        layer.msg('上传失败，请重试');
                        // 移除临时上传元素
                        $('#' + uploadId).remove();
                    }
                });
                
                // 触发文件选择
                $('#' + uploadId).click();
            });
        });
    </script>

