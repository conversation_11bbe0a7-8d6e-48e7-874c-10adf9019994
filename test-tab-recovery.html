<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>标签页恢复测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .test-button { padding: 10px 20px; margin: 5px; background: #1890ff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        .test-button:hover { background: #40a9ff; }
        .log { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 3px; font-family: monospace; white-space: pre-wrap; }
    </style>
</head>
<body>
    <h1>标签页恢复机制测试</h1>
    
    <div class="test-section">
        <h3>1. 测试标签页信息保存</h3>
        <button class="test-button" onclick="testSaveTabInfo()">保存测试标签页信息</button>
        <button class="test-button" onclick="testLoadTabInfo()">加载标签页信息</button>
        <div id="saveTestLog" class="log"></div>
    </div>
    
    <div class="test-section">
        <h3>2. 测试Hash路由解析</h3>
        <button class="test-button" onclick="testHashParsing()">测试Hash解析</button>
        <button class="test-button" onclick="simulateHashChange()">模拟Hash变化</button>
        <div id="hashTestLog" class="log"></div>
    </div>
    
    <div class="test-section">
        <h3>3. 测试URL格式转换</h3>
        <button class="test-button" onclick="testUrlConversion()">测试URL转换</button>
        <div id="urlTestLog" class="log"></div>
    </div>
    
    <div class="test-section">
        <h3>4. 当前状态信息</h3>
        <button class="test-button" onclick="showCurrentState()">显示当前状态</button>
        <div id="stateLog" class="log"></div>
    </div>

    <script>
        function log(elementId, message) {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            element.textContent += `[${timestamp}] ${message}\n`;
        }
        
        function testSaveTabInfo() {
            const tabId = 'tab_' + Date.now();
            const tabInfo = {
                id: tabId,
                title: '测试标签页 - RFQ001',
                url: 'AssignFlow/FlowApply?RFQNo=RFQ001',
                originalUrl: '../AssignFlow/FlowApply.aspx?RFQNo=RFQ001',
                timestamp: Date.now()
            };
            
            sessionStorage.setItem('tabInfo_' + tabId, JSON.stringify(tabInfo));
            sessionStorage.setItem('layuiminimenu_' + tabId, tabInfo.title);
            
            log('saveTestLog', `保存标签页信息: ${tabId}`);
            log('saveTestLog', `标题: ${tabInfo.title}`);
            log('saveTestLog', `URL: ${tabInfo.url}`);
            
            // 模拟设置hash
            window.location.hash = '#/' + tabId;
            log('saveTestLog', `设置Hash: ${window.location.hash}`);
        }
        
        function testLoadTabInfo() {
            const hash = window.location.hash;
            if (hash && hash.match(/^#\/tab_\d+$/)) {
                const tabId = hash.replace('#/', '');
                const tabInfoStr = sessionStorage.getItem('tabInfo_' + tabId);
                
                if (tabInfoStr) {
                    try {
                        const tabInfo = JSON.parse(tabInfoStr);
                        log('saveTestLog', `成功加载标签页信息: ${tabId}`);
                        log('saveTestLog', `标题: ${tabInfo.title}`);
                        log('saveTestLog', `URL: ${tabInfo.url}`);
                        log('saveTestLog', `时间戳: ${new Date(tabInfo.timestamp).toLocaleString()}`);
                    } catch (e) {
                        log('saveTestLog', `解析标签页信息失败: ${e.message}`);
                    }
                } else {
                    log('saveTestLog', `未找到标签页信息: ${tabId}`);
                }
            } else {
                log('saveTestLog', `当前Hash不是标签页格式: ${hash}`);
            }
        }
        
        function testHashParsing() {
            const testHashes = [
                '#/tab_1749002148579',
                '#/AssignFlow/FlowApply?RFQNo=RFQ001',
                '#/DataManager/QueryQuote',
                '#/',
                ''
            ];
            
            testHashes.forEach(hash => {
                log('hashTestLog', `测试Hash: "${hash}"`);
                
                if (hash.match(/^#\/tab_\d+$/)) {
                    log('hashTestLog', '  -> 匹配标签页ID格式');
                } else if (hash.indexOf('#/AssignFlow/FlowApply') > -1) {
                    log('hashTestLog', '  -> 匹配FlowApply页面格式');
                } else if (hash === '#/' || hash === '') {
                    log('hashTestLog', '  -> 首页格式');
                } else {
                    log('hashTestLog', '  -> 未知格式');
                }
            });
        }
        
        function simulateHashChange() {
            const testTabId = 'tab_' + Date.now();
            log('hashTestLog', `模拟Hash变化到: #/${testTabId}`);
            window.location.hash = '#/' + testTabId;
        }
        
        function testUrlConversion() {
            const testUrls = [
                '../AssignFlow/FlowApply.aspx?RFQNo=RFQ001',
                'AssignFlow/FlowAssign.aspx',
                'DataManager/QueryQuote.aspx?filter=active',
                'api/test.ashx',
                'AssignFlow/FlowApply?RFQNo=RFQ001'
            ];
            
            testUrls.forEach(url => {
                let cleanUrl = url;
                if (cleanUrl.indexOf('.aspx') > -1) {
                    cleanUrl = cleanUrl.replace('.aspx', '');
                    log('urlTestLog', `${url} -> ${cleanUrl} (转换)`);
                } else {
                    log('urlTestLog', `${url} -> ${cleanUrl} (无需转换)`);
                }
            });
        }
        
        function showCurrentState() {
            log('stateLog', `当前URL: ${window.location.href}`);
            log('stateLog', `当前Hash: ${window.location.hash}`);
            log('stateLog', `是否在iframe中: ${window !== window.parent}`);
            
            // 显示sessionStorage中的标签页信息
            const keys = Object.keys(sessionStorage);
            const tabInfoKeys = keys.filter(key => key.startsWith('tabInfo_'));
            const menuKeys = keys.filter(key => key.startsWith('layuiminimenu_'));
            
            log('stateLog', `SessionStorage中的标签页信息数量: ${tabInfoKeys.length}`);
            tabInfoKeys.forEach(key => {
                try {
                    const info = JSON.parse(sessionStorage.getItem(key));
                    log('stateLog', `  ${key}: ${info.title} -> ${info.url}`);
                } catch (e) {
                    log('stateLog', `  ${key}: 解析失败`);
                }
            });
            
            log('stateLog', `SessionStorage中的菜单信息数量: ${menuKeys.length}`);
            menuKeys.forEach(key => {
                log('stateLog', `  ${key}: ${sessionStorage.getItem(key)}`);
            });
        }
        
        // 页面加载时显示当前状态
        window.onload = function() {
            showCurrentState();
        };
    </script>
</body>
</html>
