﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="UserManage.aspx.cs" Inherits="WebApplication1.SystemManager.UserManage" %>

<head>
<title>用户管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../lib/layui-v2.9.21/css/layui.css" media="all">
    <link rel="stylesheet" href="../css/public.css" media="all">
</head>
<style>
    .container{
        display:flex;
        justify-content:center;
        align-items:center;
    }
    .layui-form-label{
        width:180px !important; 
    }
    </style>
<div class="layuimini-container layuimini-page-anim">
    <div class="layuimini-main">
        <fieldset class="table-search-fieldset">
            <legend>搜索信息</legend>
            <div style="margin: 20px 10px 10px 10px">
                <form class="layui-form layui-form-pane" action="">
                    <div class="layui-form-item">
                        
                        
                        <div class="layui-inline">
                            <label class="layui-form-label">用户名</label>
                            <div class="layui-input-inline">
                                <input type="text" name="UserName" autocomplete="off" class="layui-input">
                            </div>
                        </div>

                        
                        <div class="layui-inline">
                            <button type="submit" class="layui-btn layui-btn-primary"  lay-submit lay-filter="data-search-btn"><i class="layui-icon"></i> 搜 索</button>
                        </div>
                    </div>
                </form>
            </div>
        </fieldset>
        

        <script type="text/html" id="toolbarDemo">
            <div class="layui-btn-container">
                <button class="layui-btn layui-btn-normal layui-btn-sm data-add-btn" lay-event="add"> 添加 </button>
                <button class="layui-btn layui-btn-sm layui-btn-danger data-delete-btn" lay-event="delete"> 删除 </button>
            </div>
        </script>

        <table class="layui-hide" id="currentTableId" lay-filter="currentTableFilter"></table>

        <script type="text/html" id="currentTableBar">
            <a class="layui-btn layui-btn-normal layui-btn-xs data-count-edit" lay-event="edit">编辑</a>
            <a class="layui-btn layui-btn-xs layui-btn-danger data-count-delete" lay-event="delete">删除</a>
        </script>
        
    </div>
</div>
<script src="../lib/layui-v2.9.21/layui.js" charset="utf-8"></script>
<script>

    layui.use(['form', 'table', 'element'], function () {
        var $ = layui.jquery,
            form = layui.form,
            table = layui.table;

        table.render({
            elem: '#currentTableId',
            url: '../ashx/UserController.ashx?action=Query',
            toolbar: '#toolbarDemo',
            method: 'post',
            defaultToolbar: ['filter', 'exports', 'print', {
                title: '提示',
                layEvent: 'LAYTABLE_TIPS',
                icon: 'layui-icon-tips'
            }],
            cols: [[
                { type: "checkbox", width: 50 },
                { field: 'UserName', width: 150, title: '用户名', sort: true },
                { field: 'UserRole', width: 150, title: '用户权限' },
                { field: 'FlowRole', width: 150, title: '流程权限', sort: true },
                { title: '操作', minWidth: 150, toolbar: '#currentTableBar', align: "center" }
            ]],
            limits: [10, 15, 20, 25, 50, 100],
            limit: 10,
            page: true,
            skin: 'line',

        });



        // 监听搜索操作
        form.on('submit(data-search-btn)', function (data) {
            var result = data.field;
            //console.log(result.Customer);
            //layer.alert(result, {
            //    title: '最终的搜索信息'
            //});
            
            //执行搜索重载
            table.reload('currentTableId', {
                page: {
                    curr: 1
                }
                , where: result,
                error: {

                }

            }, 'data');

            return false;
        });

        /**
         * toolbar事件监听
         */
        table.on('toolbar(currentTableFilter)', function (obj) {
            if (obj.event === 'add') {   // 监听添加操作

                var index = layer.open({
                    title: '新建用户',
                    type: 2,
                    shade: 0.2,
                    maxmin: true,
                    shadeClose: true,
                    area: ['50%', '50%'],
                    content: '../SystemManager/AddUser.aspx',
                    success: function (layero, index) {
                        //console.log(data);
                        // 获取iframe页面的窗口对象
                        var iframeWindow = window['layui-layer-iframe' + index];
                        var iframeDoc = $(layero).find('iframe')[0].contentWindow.document;

                        // 在iframe页面中绑定提交事件
                        $(iframeDoc).find('.layui-form').on('submit', function (e) {
                            // 阻止表单默认提交
                            e.preventDefault();
                            return false;
                        });

                        
                        //监听提交
                        iframeWindow.layui.form.on('submit(saveBtn)', function (data) {
                            var field = data.field;
                            console.log(field);
                            $.ajax({
                                url: '../ashx/UserController.ashx?action=Add',
                                type: 'post',
                                data: field,
                                success: function (data) {
                                    console.log(data);
                                    var objdata = eval("(" + data + ")")
                                    if (objdata.result == "success") {
                                        //console.log(field);
                                        layer.msg(objdata.msg, { icon: 1, time: 2000 }, function () {
                                            layer.close(index);
                                            table.reload('currentTableId')
                                        })
                                    }
                                    else {
                                        layer.alert(objdata.msg);
                                    }

                                },
                                error: function (data) {
                                    layer.alert(objdata.msg);
                                }
                            });
                            return false;

                        });

                    },
                });
                //$(window).on("resize", function () {
                //    layer.full(index);
                //});
            } else if (obj.event === 'delete') {  // 监听删除操作
                var checkStatus = table.checkStatus('currentTableId')
                    , data = checkStatus.data;
                console.log(data);

                if (data.length == 0) {
                    layer.msg('请选择要删除的项', { icon: 0 });
                    return;
                }
                var nos = data.map(function (item) {
                    return item.UserName;
                }).join(',');
                $.ajax({
                    url: '../ashx/UserController.ashx?action=BatchDelete',
                    type: 'POST',
                    data: { nos: nos },
                    success: function (response) {
                        var res = JSON.parse(response);
                        console.log(res);
                        if (res.result === 'success') {
                            layer.msg('删除成功', { icon: 1 });
                            // 重新加载表格数据
                            table.reload('currentTableId');
                        } else {
                            layer.msg('删除失败: ' + res.msg, { icon: 2 });
                        }
                    },
                    error: function () {
                        layer.msg('删除请求失败', { icon: 2 });
                    }
                });
                //layer.alert(JSON.stringify(data));
            }
        });

        //监听表格复选框选择
        table.on('checkbox(currentTableFilter)', function (obj) {
            //console.log(obj)
        });

        table.on('tool(currentTableFilter)', function (obj) {
            var data = obj.data;
            if (obj.event === 'edit') {


                var index = layer.open({
                    title: '编辑用户',
                    type: 2,
                    shade: 0.2,
                    maxmin: true,
                    shadeClose: true,
                    area: ['50%', '50%'],
                    content: '../SystemManager/EditUser.aspx',
                    success: function (layero, index) {
                        // 获取iframe页面的窗口对象
                        var iframeWindow = window['layui-layer-iframe' + index];
                        iframeWindow.layui.form.val("editform", data);
                        //console.log(data);
                        //监听提交
                        iframeWindow.layui.form.on('submit(saveBtn)', function (data){
                            var field = data.field;
                            console.log(field);
                            $.ajax({
                                url: '../ashx/UserController.ashx?action=Update',
                                type: 'post',
                                data: field,
                                success: function (data) {
                                    var objdata = eval("(" + data + ")")
                                    if (objdata.result == "success") {
                                        //console.log(field);
                                        layer.msg(objdata.msg, { icon: 1, time: 2000 }, function () {
                                            layer.close(index);
                                            table.reload('currentTableId')
                                        })
                                    }
                                    else {
                                        layer.alert(objdata.msg);
                                    }

                                },
                                error: function (data) {
                                    layer.alert(objdata.msg);
                                }
                            });
                            return false;

                        });
                    },
                });
                $(window).on("resize", function () {
                    layer.full(index);
                });
                return false;
            } else if (obj.event === 'delete') {
                layer.confirm('真的删除用户么', function (index) {
                    var nos = data.UserName;
                    $.ajax({
                        url: '../ashx/UserController.ashx?action=BatchDelete',
                        type: 'POST',
                        data: { nos: nos },
                        success: function (response) {
                            var res = JSON.parse(response);
                            console.log(res);
                            if (res.result === 'success') {
                                layer.msg('删除成功', { icon: 1 });
                                // 重新加载表格数据
                                table.reload('currentTableId', {

                                    done: function (res, curr, count) {
                                        if (curr > 1 && res.data.length === 0) {
                                            curr = curr - 1;
                                            table.reload('currentTableId', { page: { curr: curr }, })
                                        }
                                    }
                                });
                            } else {
                                layer.msg('删除失败: ' + res.msg, { icon: 2 });
                            }
                        },
                        error: function () {
                            layer.msg('删除请求失败', { icon: 2 });
                        }
                    });
                    layer.close(index);
                });
            }
        });

    });</script>

