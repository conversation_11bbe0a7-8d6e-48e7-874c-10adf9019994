﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="ChangePassWord.aspx.cs" Inherits="WebApplication1.SystemManager.ChangePassWord" %>

<!DOCTYPE html>


<style>
    .layui-form-item .layui-input-company {
        width: auto;
        padding-right: 10px;
        line-height: 38px;
    }
</style>
<head>
<title>修改密码</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../lib/layui-v2.9.21/css/layui.css" media="all">
    <link rel="stylesheet" href="../css/public.css" media="all">
</head>
<div class="layuimini-container layuimini-page-anim">
    <div class="layuimini-main">

        <form class="layui-form layuimini-form" id="changeform">
            <div class="layui-form-item">
                <label class="layui-form-label required">旧的密码</label>
                <div class="layui-input-block">
                    <input type="password" name="old_password" lay-verify="required" lay-reqtext="旧的密码不能为空" placeholder="请输入旧的密码" value="" class="layui-input">
                    <tip>填写自己账号的旧的密码。</tip>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label required">新的密码</label>
                <div class="layui-input-block">
                    <input type="password" name="new_password" lay-verify="required" lay-reqtext="新的密码不能为空" placeholder="请输入新的密码" value="" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label required">新的密码</label>
                <div class="layui-input-block">
                    <input type="password" name="again_password" lay-verify="required" lay-reqtext="新的密码不能为空" placeholder="请输入新的密码" value="" class="layui-input">
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn layui-btn-normal" lay-submit lay-filter="saveBtn">确认保存</button>
                </div>
            </div>
        </form>
    </div>
</div>
<script src="../lib/layui-v2.9.21/layui.js" charset="utf-8"></script>
<script>
    layui.use(['form'], function () {
        var form = layui.form,
            layer = layui.layer,
            $ = layui.jquery;

        /**
         * 初始化表单，要加上，不然刷新部分组件可能会不加载
         */
        form.render();

        //监听提交
        form.on('submit(saveBtn)', function (data) {
            
            var field = data.field;
            var pwdRegex = new RegExp('(?=.*[0-9])(?=.*[a-zA-Z]).{8,30}');
            if ($("input[name='new_password']").val() != $("input[name='again_password']").val()) {
                layer.msg('新密码不一致', { icon: 0 });
                return false;
            }
            if ($("input[name='new_password']").val() == $("input[name='old_password']").val()) {
                layer.msg('新密码不能与旧密码一致', { icon: 0 });
                return false;
            }
            //if (!pwdRegex.test($("input[name='new_password']").val())) {
            //    layer.msg('密码必须包含字母数字且长度大于8小于30', { icon: 0 });
            //    return;
            //}
            $.ajax({
                url: '../ashx/UserController.ashx?action=Change',
                type: 'post',
                data: field,
                success: function (data) {
                    console.log(data);
                    var objdata = eval("(" + data + ")")
                    if (objdata.result == "success") {
                        //console.log(field);
                        layer.msg(objdata.msg, { icon: 1, time: 2000 }, function () {
                            
                            $("#changeform")[0].reset();

                            form.render();
                        })
                    }
                    else {
                        layer.alert(objdata.msg);
                    }

                },
                error: function (data) {
                    layer.alert(objdata.msg);
                }
            });
            return false;
        });

    });
</script>
